<?php
// 支付页面
require_once 'includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录';
    redirect('login.php');
}

// 获取订单ID
$orderId = intval($_GET['order_id'] ?? 0);

if ($orderId <= 0) {
    $_SESSION['error_message'] = '订单ID无效';
    redirect('my-orders.php');
}

// 获取订单信息
try {
    $stmt = $pdo->prepare("
        SELECT o.*, p.title as product_title, p.images as product_images, p.price as product_price,
               u.nickname as seller_name, u.avatar as seller_avatar
        FROM orders o
        JOIN products p ON o.product_id = p.id
        JOIN users u ON o.seller_id = u.id
        WHERE o.id = ? AND o.buyer_id = ?
    ");
    $stmt->execute([$orderId, $_SESSION['user_id']]);
    $order = $stmt->fetch();
    
    if (!$order) {
        $_SESSION['error_message'] = '订单不存在或无权限访问';
        redirect('my-orders.php');
    }
    
    if ($order['status'] !== 'pending') {
        $_SESSION['error_message'] = '订单状态不正确，无法支付';
        redirect('my-orders.php');
    }
    
} catch (Exception $e) {
    $_SESSION['error_message'] = '获取订单信息失败';
    redirect('my-orders.php');
}

$pageTitle = '订单支付';
$additionalCSS = ['css/pay.css'];
require_once 'includes/header.php';
?>

<main class="main pay-page">
    <div class="container">
        <div class="pay-container">
            <!-- 页面标题 -->
            <div class="page-header">
                <h1 class="page-title">
                    <i class="bi bi-credit-card"></i>
                    订单支付
                </h1>
                <div class="breadcrumb">
                    <a href="my-orders.php">我的订单</a>
                    <i class="bi bi-chevron-right"></i>
                    <span>支付订单</span>
                </div>
            </div>

            <div class="pay-content">
                <!-- 订单信息 -->
                <div class="order-info-section">
                    <h2 class="section-title">订单信息</h2>
                    <div class="order-card">
                        <div class="order-header">
                            <span class="order-number">订单号：<?php echo $order['order_number'] ?? 'XY' . date('YmdHis', strtotime($order['created_at'])) . str_pad($order['id'], 4, '0', STR_PAD_LEFT); ?></span>
                            <span class="order-time"><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></span>
                        </div>
                        
                        <div class="product-info">
                            <div class="product-image">
                                <?php
                                $images = json_decode($order['product_images'], true);
                                $firstImage = $images ? (UPLOAD_PATH . $images[0]) : 'images/product-default.svg';
                                ?>
                                <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($order['product_title']); ?>">
                            </div>
                            <div class="product-details">
                                <h3 class="product-title"><?php echo htmlspecialchars($order['product_title']); ?></h3>
                                <div class="seller-info">
                                    <img src="<?php echo $order['seller_avatar'] ?: 'images/avatar-default.svg'; ?>" alt="卖家头像" class="seller-avatar">
                                    <span class="seller-name"><?php echo htmlspecialchars($order['seller_name']); ?></span>
                                </div>
                                <div class="order-quantity">数量：<?php echo $order['quantity']; ?></div>
                            </div>
                            <div class="product-price">
                                <div class="unit-price">单价：<?php echo formatPrice($order['product_price']); ?></div>
                                <div class="total-price">总计：<?php echo formatPrice($order['total_price']); ?></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 支付方式选择 -->
                <div class="payment-section">
                    <h2 class="section-title">选择支付方式</h2>
                    <div class="payment-methods">
                        <div class="payment-method active" data-method="alipay">
                            <div class="method-icon">
                                <i class="bi bi-phone"></i>
                            </div>
                            <div class="method-info">
                                <div class="method-name">支付宝</div>
                                <div class="method-desc">推荐使用支付宝快捷支付</div>
                            </div>
                            <div class="method-radio">
                                <input type="radio" name="payment_method" value="alipay" checked>
                            </div>
                        </div>
                        
                        <div class="payment-method" data-method="wechat">
                            <div class="method-icon">
                                <i class="bi bi-wechat"></i>
                            </div>
                            <div class="method-info">
                                <div class="method-name">微信支付</div>
                                <div class="method-desc">使用微信扫码支付</div>
                            </div>
                            <div class="method-radio">
                                <input type="radio" name="payment_method" value="wechat">
                            </div>
                        </div>
                        
                        <div class="payment-method" data-method="bank">
                            <div class="method-icon">
                                <i class="bi bi-credit-card"></i>
                            </div>
                            <div class="method-info">
                                <div class="method-name">银行卡</div>
                                <div class="method-desc">支持各大银行储蓄卡和信用卡</div>
                            </div>
                            <div class="method-radio">
                                <input type="radio" name="payment_method" value="bank">
                            </div>
                        </div>

                        <div class="payment-method" data-method="epay">
                            <div class="method-icon">
                                <i class="bi bi-lightning-charge"></i>
                            </div>
                            <div class="method-info">
                                <div class="method-name">易支付</div>
                                <div class="method-desc">快速便捷的第三方支付平台</div>
                            </div>
                            <div class="method-radio">
                                <input type="radio" name="payment_method" value="epay">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 支付金额确认 -->
                <div class="payment-summary">
                    <div class="summary-row">
                        <span class="label">商品金额：</span>
                        <span class="value"><?php echo formatPrice($order['total_price']); ?></span>
                    </div>
                    <div class="summary-row">
                        <span class="label">运费：</span>
                        <span class="value free">免费</span>
                    </div>
                    <div class="summary-row total">
                        <span class="label">应付金额：</span>
                        <span class="value"><?php echo formatPrice($order['total_price']); ?></span>
                    </div>
                </div>

                <!-- 支付按钮 -->
                <div class="payment-actions">
                    <button class="btn-cancel" onclick="cancelPayment()">取消支付</button>
                    <button class="btn-pay" onclick="processPay(<?php echo $order['id']; ?>)">
                        立即支付 <?php echo formatPrice($order['total_price']); ?>
                    </button>
                </div>
            </div>
        </div>
    </div>
</main>

<!-- 支付处理模态框 -->
<div id="paymentModal" class="modal">
    <div class="modal-content">
        <div class="payment-processing">
            <div class="processing-icon">
                <i class="bi bi-credit-card"></i>
            </div>
            <h3>正在处理支付...</h3>
            <p>请稍候，正在为您处理支付请求</p>
            <div class="loading-spinner"></div>
        </div>
    </div>
</div>

<script>
// 支付方式选择
document.querySelectorAll('.payment-method').forEach(method => {
    method.addEventListener('click', function() {
        // 移除所有active状态
        document.querySelectorAll('.payment-method').forEach(m => m.classList.remove('active'));
        // 添加当前选中状态
        this.classList.add('active');
        // 选中对应的radio
        this.querySelector('input[type="radio"]').checked = true;
    });
});

// 取消支付
function cancelPayment() {
    if (confirm('确定要取消支付吗？')) {
        window.location.href = 'my-orders.php';
    }
}

// 处理支付
function processPay(orderId) {
    const selectedMethod = document.querySelector('input[name="payment_method"]:checked').value;
    
    // 显示支付处理模态框
    document.getElementById('paymentModal').style.display = 'block';
    
    // 模拟支付处理（实际应该调用真实支付接口）
    setTimeout(() => {
        const formData = new FormData();
        formData.append('action', 'pay');
        formData.append('order_id', orderId);
        formData.append('payment_method', selectedMethod);
        
        fetch('api/order-actions.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('paymentModal').style.display = 'none';

            if (data.success) {
                if (data.payment_type === 'redirect' && data.payment_url) {
                    // 易支付跳转
                    showMessage('正在跳转到支付页面...', 'info');
                    setTimeout(() => {
                        window.location.href = data.payment_url;
                    }, 1000);
                } else {
                    // 其他支付方式
                    showMessage('支付成功！', 'success');
                    setTimeout(() => {
                        window.location.href = 'my-orders.php?status=paid';
                    }, 1500);
                }
            } else {
                showMessage(data.message || '支付失败', 'error');
            }
        })
        .catch(error => {
            document.getElementById('paymentModal').style.display = 'none';
            console.error('Payment error:', error);
            showMessage('支付处理失败，请重试', 'error');
        });
    }, 2000); // 模拟2秒支付处理时间
}

// 消息提示函数
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-toast message-${type}`;
    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);
    
    setTimeout(() => messageDiv.classList.add('show'), 100);
    setTimeout(() => {
        messageDiv.classList.remove('show');
        setTimeout(() => messageDiv.remove(), 300);
    }, 3000);
}
</script>

<?php require_once 'includes/footer.php'; ?>
