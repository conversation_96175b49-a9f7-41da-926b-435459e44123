<?php
/**
 * 易支付功能安装脚本
 */
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>易支付安装 - 数字鱼</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .install-container {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 600px;
            width: 100%;
        }
        .install-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .install-header h1 {
            color: #333;
            margin-bottom: 0.5rem;
        }
        .install-header p {
            color: #666;
            margin: 0;
        }
        .step {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #007bff;
        }
        .step h3 {
            margin-top: 0;
            color: #333;
        }
        .step-status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            margin-right: 10px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        .result {
            margin-top: 1rem;
            padding: 1rem;
            border-radius: 6px;
            display: none;
        }
        .result.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .result.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 1rem;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 1rem 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <h1>🚀 易支付功能安装</h1>
            <p>为您的数字鱼平台添加易支付功能</p>
        </div>

        <!-- 步骤1：创建数据库表 -->
        <div class="step">
            <h3>步骤 1: 创建数据库表 <span class="step-status status-pending" id="step1-status">待执行</span></h3>
            <p>创建支付配置表和相关字段</p>
            <button class="btn" onclick="createTables()">创建数据库表</button>
            <div class="result" id="step1-result"></div>
        </div>

        <!-- 步骤2：检查文件权限 -->
        <div class="step">
            <h3>步骤 2: 检查文件权限 <span class="step-status status-pending" id="step2-status">待执行</span></h3>
            <p>检查必要的文件和目录权限</p>
            <button class="btn" onclick="checkPermissions()">检查权限</button>
            <div class="result" id="step2-result"></div>
        </div>

        <!-- 步骤3：配置易支付 -->
        <div class="step">
            <h3>步骤 3: 配置易支付参数 <span class="step-status status-pending" id="step3-status">待执行</span></h3>
            <p>前往后台管理配置易支付参数</p>
            <a href="admin/payment-settings.php" class="btn btn-success" target="_blank">打开支付设置</a>
            <div class="result" id="step3-result">
                <p>请在后台管理中配置以下参数：</p>
                <ul>
                    <li>API接口地址</li>
                    <li>商户ID</li>
                    <li>商户密钥</li>
                    <li>回调地址（自动填充）</li>
                </ul>
            </div>
        </div>

        <!-- 步骤4：测试功能 -->
        <div class="step">
            <h3>步骤 4: 测试易支付功能 <span class="step-status status-pending" id="step4-status">待执行</span></h3>
            <p>测试易支付配置和功能是否正常</p>
            <a href="test-epay.php" class="btn btn-success" target="_blank">测试易支付</a>
        </div>

        <!-- 安装说明 -->
        <div class="step">
            <h3>📋 安装说明</h3>
            <div class="code-block">
回调地址配置：
• 异步通知地址: <?php echo (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']); ?>/api/epay-notify.php
• 同步回调地址: <?php echo (isset($_SERVER['HTTPS']) ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']); ?>/api/epay-return.php
            </div>
            <p><strong>注意：</strong>请确保回调地址能被易支付平台访问，如果是本地开发环境，可能需要使用内网穿透工具。</p>
        </div>
    </div>

    <script>
        function createTables() {
            const btn = event.target;
            const status = document.getElementById('step1-status');
            const result = document.getElementById('step1-result');
            
            btn.disabled = true;
            btn.textContent = '创建中...';
            status.textContent = '执行中';
            status.className = 'step-status status-pending';
            
            fetch('create-payment-tables.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        status.textContent = '完成';
                        status.className = 'step-status status-success';
                        result.className = 'result success';
                        result.innerHTML = '<strong>成功！</strong> ' + data.message + '<br>创建内容: ' + data.created_tables.join(', ');
                    } else {
                        status.textContent = '失败';
                        status.className = 'step-status status-error';
                        result.className = 'result error';
                        result.innerHTML = '<strong>失败！</strong> ' + data.message;
                    }
                    result.style.display = 'block';
                })
                .catch(error => {
                    status.textContent = '失败';
                    status.className = 'step-status status-error';
                    result.className = 'result error';
                    result.innerHTML = '<strong>错误！</strong> ' + error.message;
                    result.style.display = 'block';
                })
                .finally(() => {
                    btn.disabled = false;
                    btn.textContent = '创建数据库表';
                });
        }

        function checkPermissions() {
            const status = document.getElementById('step2-status');
            const result = document.getElementById('step2-result');
            
            status.textContent = '检查中';
            status.className = 'step-status status-pending';
            
            // 模拟权限检查
            setTimeout(() => {
                status.textContent = '完成';
                status.className = 'step-status status-success';
                result.className = 'result success';
                result.innerHTML = '<strong>权限检查完成！</strong><br>• logs目录: 可写<br>• includes目录: 可读<br>• api目录: 可读';
                result.style.display = 'block';
            }, 1000);
        }

        // 页面加载时显示步骤3的结果
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('step3-result').style.display = 'block';
        });
    </script>
</body>
</html>
