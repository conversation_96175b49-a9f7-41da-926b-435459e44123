/**
 * 统一的消息通知系统
 * 提供现代化的用户体验反馈
 */

class NotificationSystem {
    constructor() {
        this.container = null;
        this.notifications = new Map();
        this.init();
    }

    init() {
        // 创建通知容器
        this.container = document.createElement('div');
        this.container.className = 'notification-container';
        document.body.appendChild(this.container);

        // 添加样式
        this.addStyles();
    }

    addStyles() {
        if (document.getElementById('notification-styles')) return;

        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            .notification-container {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 10000;
                pointer-events: none;
            }

            .notification {
                background: white;
                border-radius: 12px;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
                margin-bottom: 12px;
                padding: 16px 20px;
                min-width: 320px;
                max-width: 400px;
                transform: translateX(100%);
                opacity: 0;
                transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                pointer-events: auto;
                border-left: 4px solid #007bff;
                position: relative;
                overflow: hidden;
            }

            .notification.show {
                transform: translateX(0);
                opacity: 1;
            }

            .notification.success {
                border-left-color: #28a745;
            }

            .notification.error {
                border-left-color: #dc3545;
            }

            .notification.warning {
                border-left-color: #ffc107;
            }

            .notification.info {
                border-left-color: #17a2b8;
            }

            .notification-header {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 8px;
            }

            .notification-icon {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 14px;
                color: white;
                flex-shrink: 0;
            }

            .notification.success .notification-icon {
                background: #28a745;
            }

            .notification.error .notification-icon {
                background: #dc3545;
            }

            .notification.warning .notification-icon {
                background: #ffc107;
            }

            .notification.info .notification-icon {
                background: #17a2b8;
            }

            .notification-title {
                font-weight: 600;
                color: #212529;
                font-size: 14px;
                flex: 1;
            }

            .notification-close {
                width: 20px;
                height: 20px;
                border: none;
                background: none;
                color: #6c757d;
                cursor: pointer;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 16px;
                transition: all 0.2s ease;
            }

            .notification-close:hover {
                background: #f8f9fa;
                color: #495057;
            }

            .notification-content {
                color: #6c757d;
                font-size: 13px;
                line-height: 1.4;
                margin-bottom: 12px;
            }

            .notification-actions {
                display: flex;
                gap: 8px;
                justify-content: flex-end;
            }

            .notification-btn {
                padding: 6px 12px;
                border: 1px solid #dee2e6;
                background: white;
                border-radius: 6px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .notification-btn.primary {
                background: #007bff;
                border-color: #007bff;
                color: white;
            }

            .notification-btn:hover {
                background: #f8f9fa;
            }

            .notification-btn.primary:hover {
                background: #0056b3;
            }

            .notification-progress {
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background: rgba(0, 123, 255, 0.3);
                transition: width linear;
            }

            .notification.success .notification-progress {
                background: rgba(40, 167, 69, 0.3);
            }

            .notification.error .notification-progress {
                background: rgba(220, 53, 69, 0.3);
            }

            .notification.warning .notification-progress {
                background: rgba(255, 193, 7, 0.3);
            }

            /* 移动端适配 */
            @media (max-width: 768px) {
                .notification-container {
                    top: 10px;
                    right: 10px;
                    left: 10px;
                }

                .notification {
                    min-width: auto;
                    max-width: none;
                    margin-bottom: 8px;
                }
            }

            /* 加载动画 */
            .loading-spinner {
                width: 16px;
                height: 16px;
                border: 2px solid #f3f3f3;
                border-top: 2px solid #007bff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    show(options) {
        const {
            type = 'info',
            title = '',
            message = '',
            duration = 5000,
            actions = [],
            showProgress = true,
            persistent = false
        } = options;

        const id = Date.now() + Math.random();
        const notification = this.createNotification(id, type, title, message, actions, persistent);
        
        this.container.appendChild(notification);
        this.notifications.set(id, notification);

        // 显示动画
        requestAnimationFrame(() => {
            notification.classList.add('show');
        });

        // 自动关闭
        if (!persistent && duration > 0) {
            this.startAutoClose(id, duration, showProgress);
        }

        return id;
    }

    createNotification(id, type, title, message, actions, persistent) {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.dataset.id = id;

        const iconMap = {
            success: 'bi-check',
            error: 'bi-x',
            warning: 'bi-exclamation',
            info: 'bi-info'
        };

        notification.innerHTML = `
            <div class="notification-header">
                <div class="notification-icon">
                    <i class="bi ${iconMap[type]}"></i>
                </div>
                <div class="notification-title">${title}</div>
                ${!persistent ? '<button class="notification-close">&times;</button>' : ''}
            </div>
            ${message ? `<div class="notification-content">${message}</div>` : ''}
            ${actions.length > 0 ? this.createActions(actions) : ''}
            <div class="notification-progress"></div>
        `;

        // 绑定关闭事件
        const closeBtn = notification.querySelector('.notification-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.close(id));
        }

        // 绑定动作按钮事件
        actions.forEach((action, index) => {
            const btn = notification.querySelector(`[data-action="${index}"]`);
            if (btn && action.handler) {
                btn.addEventListener('click', () => {
                    action.handler();
                    if (action.closeOnClick !== false) {
                        this.close(id);
                    }
                });
            }
        });

        return notification;
    }

    createActions(actions) {
        const actionsHtml = actions.map((action, index) => 
            `<button class="notification-btn ${action.primary ? 'primary' : ''}" data-action="${index}">
                ${action.text}
            </button>`
        ).join('');

        return `<div class="notification-actions">${actionsHtml}</div>`;
    }

    startAutoClose(id, duration, showProgress) {
        const notification = this.notifications.get(id);
        if (!notification) return;

        const progressBar = notification.querySelector('.notification-progress');
        
        if (showProgress && progressBar) {
            progressBar.style.width = '100%';
            progressBar.style.transition = `width ${duration}ms linear`;
            
            requestAnimationFrame(() => {
                progressBar.style.width = '0%';
            });
        }

        setTimeout(() => {
            this.close(id);
        }, duration);
    }

    close(id) {
        const notification = this.notifications.get(id);
        if (!notification) return;

        notification.classList.remove('show');
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            this.notifications.delete(id);
        }, 400);
    }

    closeAll() {
        this.notifications.forEach((notification, id) => {
            this.close(id);
        });
    }

    // 便捷方法
    success(title, message, options = {}) {
        return this.show({ ...options, type: 'success', title, message });
    }

    error(title, message, options = {}) {
        return this.show({ ...options, type: 'error', title, message });
    }

    warning(title, message, options = {}) {
        return this.show({ ...options, type: 'warning', title, message });
    }

    info(title, message, options = {}) {
        return this.show({ ...options, type: 'info', title, message });
    }

    loading(title, message = '请稍候...') {
        return this.show({
            type: 'info',
            title,
            message: `<div style="display: flex; align-items: center; gap: 8px;">
                <div class="loading-spinner"></div>
                ${message}
            </div>`,
            persistent: true,
            showProgress: false
        });
    }
}

// 创建全局实例
window.notify = new NotificationSystem();

// 兼容旧的showMessage函数
window.showMessage = function(message, type = 'info') {
    const titleMap = {
        success: '操作成功',
        error: '操作失败',
        warning: '注意',
        info: '提示'
    };
    
    window.notify.show({
        type,
        title: titleMap[type],
        message
    });
};
