/**
 * 加载状态管理系统
 * 提供统一的加载指示和进度反馈
 */

class LoadingSystem {
    constructor() {
        this.loadingStates = new Map();
        this.init();
    }

    init() {
        this.addStyles();
    }

    addStyles() {
        if (document.getElementById('loading-styles')) return;

        const style = document.createElement('style');
        style.id = 'loading-styles';
        style.textContent = `
            /* 全屏加载遮罩 */
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(255, 255, 255, 0.9);
                backdrop-filter: blur(4px);
                z-index: 9999;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .loading-overlay.show {
                opacity: 1;
            }

            .loading-content {
                background: white;
                border-radius: 16px;
                padding: 32px;
                box-shadow: 0 16px 64px rgba(0, 0, 0, 0.1);
                text-align: center;
                max-width: 320px;
                width: 90%;
            }

            .loading-spinner-large {
                width: 48px;
                height: 48px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #007bff;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 0 auto 16px;
            }

            .loading-title {
                font-size: 18px;
                font-weight: 600;
                color: #212529;
                margin-bottom: 8px;
            }

            .loading-message {
                font-size: 14px;
                color: #6c757d;
                line-height: 1.4;
            }

            /* 按钮加载状态 */
            .btn-loading {
                position: relative;
                pointer-events: none;
                opacity: 0.7;
            }

            .btn-loading::before {
                content: '';
                position: absolute;
                top: 50%;
                left: 50%;
                width: 16px;
                height: 16px;
                margin: -8px 0 0 -8px;
                border: 2px solid transparent;
                border-top: 2px solid currentColor;
                border-radius: 50%;
                animation: spin 1s linear infinite;
            }

            .btn-loading .btn-text {
                opacity: 0;
            }

            /* 内联加载指示器 */
            .loading-inline {
                display: inline-flex;
                align-items: center;
                gap: 8px;
                font-size: 14px;
                color: #6c757d;
            }

            .loading-dots {
                display: inline-flex;
                gap: 2px;
            }

            .loading-dot {
                width: 4px;
                height: 4px;
                border-radius: 50%;
                background: #007bff;
                animation: loading-dots 1.4s ease-in-out infinite both;
            }

            .loading-dot:nth-child(1) { animation-delay: -0.32s; }
            .loading-dot:nth-child(2) { animation-delay: -0.16s; }

            @keyframes loading-dots {
                0%, 80%, 100% {
                    transform: scale(0.8);
                    opacity: 0.5;
                }
                40% {
                    transform: scale(1);
                    opacity: 1;
                }
            }

            /* 进度条 */
            .progress-bar {
                width: 100%;
                height: 8px;
                background: #e9ecef;
                border-radius: 4px;
                overflow: hidden;
                margin: 16px 0;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #007bff, #0056b3);
                border-radius: 4px;
                transition: width 0.3s ease;
                position: relative;
            }

            .progress-fill::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                bottom: 0;
                right: 0;
                background: linear-gradient(
                    90deg,
                    transparent,
                    rgba(255, 255, 255, 0.4),
                    transparent
                );
                animation: progress-shine 2s infinite;
            }

            @keyframes progress-shine {
                0% { transform: translateX(-100%); }
                100% { transform: translateX(100%); }
            }

            /* 骨架屏 */
            .skeleton {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: skeleton-loading 1.5s infinite;
            }

            @keyframes skeleton-loading {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }

            .skeleton-text {
                height: 16px;
                border-radius: 4px;
                margin-bottom: 8px;
            }

            .skeleton-text.short {
                width: 60%;
            }

            .skeleton-text.medium {
                width: 80%;
            }

            .skeleton-text.long {
                width: 100%;
            }

            .skeleton-avatar {
                width: 48px;
                height: 48px;
                border-radius: 50%;
            }

            .skeleton-image {
                width: 100%;
                height: 200px;
                border-radius: 8px;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        `;
        document.head.appendChild(style);
    }

    // 显示全屏加载
    showOverlay(title = '加载中', message = '请稍候...') {
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="loading-content">
                <div class="loading-spinner-large"></div>
                <div class="loading-title">${title}</div>
                <div class="loading-message">${message}</div>
            </div>
        `;

        document.body.appendChild(overlay);
        
        requestAnimationFrame(() => {
            overlay.classList.add('show');
        });

        return overlay;
    }

    // 隐藏全屏加载
    hideOverlay(overlay) {
        if (!overlay) return;
        
        overlay.classList.remove('show');
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
    }

    // 按钮加载状态
    setButtonLoading(button, loading = true) {
        if (loading) {
            button.classList.add('btn-loading');
            button.disabled = true;
            
            // 保存原始文本
            if (!button.dataset.originalText) {
                button.dataset.originalText = button.innerHTML;
            }
            
            button.innerHTML = `<span class="btn-text">${button.dataset.originalText}</span>`;
        } else {
            button.classList.remove('btn-loading');
            button.disabled = false;
            
            if (button.dataset.originalText) {
                button.innerHTML = button.dataset.originalText;
            }
        }
    }

    // 创建内联加载指示器
    createInlineLoader(text = '加载中') {
        const loader = document.createElement('div');
        loader.className = 'loading-inline';
        loader.innerHTML = `
            <span>${text}</span>
            <div class="loading-dots">
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
                <div class="loading-dot"></div>
            </div>
        `;
        return loader;
    }

    // 创建进度条
    createProgressBar(progress = 0) {
        const progressBar = document.createElement('div');
        progressBar.className = 'progress-bar';
        progressBar.innerHTML = `<div class="progress-fill" style="width: ${progress}%"></div>`;
        return progressBar;
    }

    // 更新进度条
    updateProgress(progressBar, progress) {
        const fill = progressBar.querySelector('.progress-fill');
        if (fill) {
            fill.style.width = `${Math.max(0, Math.min(100, progress))}%`;
        }
    }

    // 创建骨架屏
    createSkeleton(type = 'text', options = {}) {
        const skeleton = document.createElement('div');
        skeleton.className = 'skeleton';

        switch (type) {
            case 'text':
                skeleton.className += ' skeleton-text';
                if (options.size) {
                    skeleton.className += ` ${options.size}`;
                }
                break;
            case 'avatar':
                skeleton.className += ' skeleton-avatar';
                break;
            case 'image':
                skeleton.className += ' skeleton-image';
                if (options.height) {
                    skeleton.style.height = options.height;
                }
                break;
        }

        return skeleton;
    }

    // 表单提交加载状态
    handleFormSubmit(form, options = {}) {
        const {
            loadingText = '提交中...',
            successText = '提交成功',
            errorText = '提交失败',
            onSuccess = null,
            onError = null
        } = options;

        const submitBtn = form.querySelector('[type="submit"]');
        if (!submitBtn) return;

        // 设置按钮加载状态
        this.setButtonLoading(submitBtn, true);

        // 创建加载通知
        const loadingId = window.notify.loading('处理中', loadingText);

        return {
            success: (message = successText) => {
                this.setButtonLoading(submitBtn, false);
                window.notify.close(loadingId);
                window.notify.success('操作成功', message);
                if (onSuccess) onSuccess();
            },
            error: (message = errorText) => {
                this.setButtonLoading(submitBtn, false);
                window.notify.close(loadingId);
                window.notify.error('操作失败', message);
                if (onError) onError();
            }
        };
    }

    // AJAX请求加载状态
    wrapAjaxRequest(requestPromise, options = {}) {
        const {
            showOverlay = false,
            overlayTitle = '加载中',
            overlayMessage = '请稍候...',
            showNotification = true,
            successMessage = '操作成功',
            errorMessage = '操作失败'
        } = options;

        let overlay = null;
        let loadingId = null;

        if (showOverlay) {
            overlay = this.showOverlay(overlayTitle, overlayMessage);
        } else if (showNotification) {
            loadingId = window.notify.loading('处理中', overlayMessage);
        }

        return requestPromise
            .then(response => {
                if (overlay) {
                    this.hideOverlay(overlay);
                } else if (loadingId) {
                    window.notify.close(loadingId);
                }

                if (showNotification && successMessage) {
                    window.notify.success('操作成功', successMessage);
                }

                return response;
            })
            .catch(error => {
                if (overlay) {
                    this.hideOverlay(overlay);
                } else if (loadingId) {
                    window.notify.close(loadingId);
                }

                if (showNotification) {
                    window.notify.error('操作失败', errorMessage);
                }

                throw error;
            });
    }
}

// 创建全局实例
window.loading = new LoadingSystem();
