/**
 * UX设置管理系统
 * 允许用户自定义体验设置
 */

class UXSettings {
    constructor() {
        this.settings = this.loadSettings();
        this.init();
    }

    init() {
        this.applySettings();
        this.createSettingsPanel();
    }

    // 默认设置
    getDefaultSettings() {
        return {
            animations: true,
            notifications: true,
            sounds: false,
            reducedMotion: false,
            highContrast: false,
            fontSize: 'normal',
            theme: 'auto',
            autoSave: true,
            confirmActions: true,
            showHints: true,
            keyboardShortcuts: true
        };
    }

    // 加载设置
    loadSettings() {
        try {
            const saved = localStorage.getItem('ux_settings');
            return saved ? { ...this.getDefaultSettings(), ...JSON.parse(saved) } : this.getDefaultSettings();
        } catch (e) {
            return this.getDefaultSettings();
        }
    }

    // 保存设置
    saveSettings() {
        try {
            localStorage.setItem('ux_settings', JSON.stringify(this.settings));
            this.applySettings();
        } catch (e) {
            console.error('Failed to save UX settings:', e);
        }
    }

    // 应用设置
    applySettings() {
        const { settings } = this;
        
        // 动画设置
        if (!settings.animations || settings.reducedMotion) {
            document.documentElement.style.setProperty('--animation-duration', '0.01ms');
            document.documentElement.style.setProperty('--transition-duration', '0.01ms');
        } else {
            document.documentElement.style.removeProperty('--animation-duration');
            document.documentElement.style.removeProperty('--transition-duration');
        }

        // 高对比度
        document.documentElement.classList.toggle('high-contrast', settings.highContrast);

        // 字体大小
        document.documentElement.classList.remove('font-small', 'font-large', 'font-xlarge');
        if (settings.fontSize !== 'normal') {
            document.documentElement.classList.add(`font-${settings.fontSize}`);
        }

        // 主题
        this.applyTheme(settings.theme);

        // 通知设置
        if (window.notify) {
            window.notify.enabled = settings.notifications;
        }

        // 键盘快捷键
        document.documentElement.classList.toggle('keyboard-shortcuts-disabled', !settings.keyboardShortcuts);
    }

    // 应用主题
    applyTheme(theme) {
        document.documentElement.classList.remove('theme-light', 'theme-dark');
        
        if (theme === 'auto') {
            // 跟随系统
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            document.documentElement.classList.add(prefersDark ? 'theme-dark' : 'theme-light');
        } else {
            document.documentElement.classList.add(`theme-${theme}`);
        }
    }

    // 创建设置面板
    createSettingsPanel() {
        if (document.getElementById('ux-settings-panel')) return;

        const panel = document.createElement('div');
        panel.id = 'ux-settings-panel';
        panel.className = 'ux-settings-panel';
        panel.innerHTML = this.getSettingsPanelHTML();

        document.body.appendChild(panel);
        this.bindSettingsEvents(panel);
        this.addSettingsStyles();
    }

    getSettingsPanelHTML() {
        const { settings } = this;
        
        return `
            <div class="settings-overlay">
                <div class="settings-modal">
                    <div class="settings-header">
                        <h3>用户体验设置</h3>
                        <button class="settings-close">&times;</button>
                    </div>
                    <div class="settings-body">
                        <div class="settings-section">
                            <h4>视觉效果</h4>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" ${settings.animations ? 'checked' : ''} data-setting="animations">
                                    启用动画效果
                                </label>
                                <small>页面切换和交互动画</small>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" ${settings.reducedMotion ? 'checked' : ''} data-setting="reducedMotion">
                                    减少动画
                                </label>
                                <small>适合对动画敏感的用户</small>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" ${settings.highContrast ? 'checked' : ''} data-setting="highContrast">
                                    高对比度模式
                                </label>
                                <small>提高文字和背景的对比度</small>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h4>字体大小</h4>
                            <div class="setting-item">
                                <select data-setting="fontSize">
                                    <option value="small" ${settings.fontSize === 'small' ? 'selected' : ''}>小</option>
                                    <option value="normal" ${settings.fontSize === 'normal' ? 'selected' : ''}>正常</option>
                                    <option value="large" ${settings.fontSize === 'large' ? 'selected' : ''}>大</option>
                                    <option value="xlarge" ${settings.fontSize === 'xlarge' ? 'selected' : ''}>特大</option>
                                </select>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h4>主题</h4>
                            <div class="setting-item">
                                <select data-setting="theme">
                                    <option value="auto" ${settings.theme === 'auto' ? 'selected' : ''}>跟随系统</option>
                                    <option value="light" ${settings.theme === 'light' ? 'selected' : ''}>浅色模式</option>
                                    <option value="dark" ${settings.theme === 'dark' ? 'selected' : ''}>深色模式</option>
                                </select>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h4>交互设置</h4>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" ${settings.notifications ? 'checked' : ''} data-setting="notifications">
                                    显示通知消息
                                </label>
                                <small>操作成功或失败的提示</small>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" ${settings.sounds ? 'checked' : ''} data-setting="sounds">
                                    启用声音提示
                                </label>
                                <small>操作时的音效反馈</small>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" ${settings.confirmActions ? 'checked' : ''} data-setting="confirmActions">
                                    确认重要操作
                                </label>
                                <small>删除等操作前显示确认对话框</small>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" ${settings.showHints ? 'checked' : ''} data-setting="showHints">
                                    显示操作提示
                                </label>
                                <small>新功能和操作指导</small>
                            </div>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" ${settings.keyboardShortcuts ? 'checked' : ''} data-setting="keyboardShortcuts">
                                    启用键盘快捷键
                                </label>
                                <small>Ctrl+K搜索等快捷操作</small>
                            </div>
                        </div>

                        <div class="settings-section">
                            <h4>其他</h4>
                            <div class="setting-item">
                                <label>
                                    <input type="checkbox" ${settings.autoSave ? 'checked' : ''} data-setting="autoSave">
                                    自动保存设置
                                </label>
                                <small>自动保存表单内容和偏好设置</small>
                            </div>
                        </div>
                    </div>
                    <div class="settings-footer">
                        <button class="btn-reset">恢复默认</button>
                        <button class="btn-save">保存设置</button>
                    </div>
                </div>
            </div>
        `;
    }

    bindSettingsEvents(panel) {
        // 关闭按钮
        panel.querySelector('.settings-close').addEventListener('click', () => {
            this.hideSettings();
        });

        // 点击遮罩关闭
        panel.querySelector('.settings-overlay').addEventListener('click', (e) => {
            if (e.target === e.currentTarget) {
                this.hideSettings();
            }
        });

        // 设置项变化
        panel.addEventListener('change', (e) => {
            const setting = e.target.dataset.setting;
            if (!setting) return;

            if (e.target.type === 'checkbox') {
                this.settings[setting] = e.target.checked;
            } else {
                this.settings[setting] = e.target.value;
            }

            this.saveSettings();
        });

        // 恢复默认
        panel.querySelector('.btn-reset').addEventListener('click', () => {
            this.resetSettings();
        });

        // 保存设置
        panel.querySelector('.btn-save').addEventListener('click', () => {
            this.hideSettings();
            window.notify.success('设置已保存', '您的偏好设置已更新');
        });
    }

    addSettingsStyles() {
        if (document.getElementById('ux-settings-styles')) return;

        const style = document.createElement('style');
        style.id = 'ux-settings-styles';
        style.textContent = `
            .ux-settings-panel {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10005;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .ux-settings-panel.show {
                opacity: 1;
                visibility: visible;
            }

            .settings-overlay {
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
            }

            .settings-modal {
                background: white;
                border-radius: 12px;
                max-width: 500px;
                width: 100%;
                max-height: 80vh;
                overflow: hidden;
                transform: scale(0.9);
                transition: transform 0.3s ease;
            }

            .ux-settings-panel.show .settings-modal {
                transform: scale(1);
            }

            .settings-header {
                padding: 20px;
                border-bottom: 1px solid #e9ecef;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .settings-header h3 {
                margin: 0;
                color: #212529;
            }

            .settings-close {
                background: none;
                border: none;
                font-size: 24px;
                cursor: pointer;
                color: #6c757d;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
            }

            .settings-close:hover {
                background: #f8f9fa;
            }

            .settings-body {
                padding: 20px;
                max-height: 60vh;
                overflow-y: auto;
            }

            .settings-section {
                margin-bottom: 24px;
            }

            .settings-section h4 {
                margin: 0 0 12px 0;
                color: #495057;
                font-size: 16px;
            }

            .setting-item {
                margin-bottom: 16px;
            }

            .setting-item label {
                display: flex;
                align-items: center;
                gap: 8px;
                cursor: pointer;
                font-weight: 500;
                color: #212529;
            }

            .setting-item small {
                display: block;
                color: #6c757d;
                margin-top: 4px;
                margin-left: 24px;
            }

            .setting-item select {
                width: 100%;
                padding: 8px 12px;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                background: white;
            }

            .settings-footer {
                padding: 20px;
                border-top: 1px solid #e9ecef;
                display: flex;
                justify-content: space-between;
                gap: 12px;
            }

            .settings-footer button {
                padding: 8px 16px;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
            }

            .btn-reset {
                background: white;
                color: #6c757d;
            }

            .btn-reset:hover {
                background: #f8f9fa;
            }

            .btn-save {
                background: #007bff;
                color: white;
                border-color: #007bff;
            }

            .btn-save:hover {
                background: #0056b3;
            }

            /* 字体大小样式 */
            .font-small { font-size: 14px; }
            .font-large { font-size: 18px; }
            .font-xlarge { font-size: 22px; }

            /* 高对比度样式 */
            .high-contrast {
                filter: contrast(150%);
            }

            /* 主题样式 */
            .theme-dark {
                --bg-color: #1a1a1a;
                --text-color: #ffffff;
                --border-color: #333333;
            }

            @media (max-width: 768px) {
                .settings-modal {
                    margin: 10px;
                    max-width: none;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 显示设置面板
    showSettings() {
        const panel = document.getElementById('ux-settings-panel');
        if (panel) {
            panel.classList.add('show');
        }
    }

    // 隐藏设置面板
    hideSettings() {
        const panel = document.getElementById('ux-settings-panel');
        if (panel) {
            panel.classList.remove('show');
        }
    }

    // 重置设置
    resetSettings() {
        this.settings = this.getDefaultSettings();
        this.saveSettings();
        
        // 重新创建面板
        const panel = document.getElementById('ux-settings-panel');
        if (panel) {
            panel.remove();
        }
        this.createSettingsPanel();
        this.showSettings();
        
        window.notify.info('设置已重置', '所有设置已恢复为默认值');
    }

    // 获取设置值
    get(key) {
        return this.settings[key];
    }

    // 设置值
    set(key, value) {
        this.settings[key] = value;
        this.saveSettings();
    }
}

// 创建全局实例
window.uxSettings = new UXSettings();

// 添加设置按钮到页面
document.addEventListener('DOMContentLoaded', function() {
    // 创建设置按钮
    const settingsBtn = document.createElement('button');
    settingsBtn.className = 'ux-settings-btn';
    settingsBtn.innerHTML = '<i class="bi bi-gear"></i>';
    settingsBtn.title = '用户体验设置';
    settingsBtn.style.cssText = `
        position: fixed;
        bottom: 80px;
        right: 20px;
        width: 48px;
        height: 48px;
        border: none;
        border-radius: 50%;
        background: #6c757d;
        color: white;
        font-size: 18px;
        cursor: pointer;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
    `;

    settingsBtn.addEventListener('click', () => {
        window.uxSettings.showSettings();
    });

    settingsBtn.addEventListener('mouseenter', () => {
        settingsBtn.style.background = '#495057';
        settingsBtn.style.transform = 'scale(1.1)';
    });

    settingsBtn.addEventListener('mouseleave', () => {
        settingsBtn.style.background = '#6c757d';
        settingsBtn.style.transform = 'scale(1)';
    });

    document.body.appendChild(settingsBtn);
});
