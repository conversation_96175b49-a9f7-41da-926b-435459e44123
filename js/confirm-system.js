/**
 * 操作确认系统
 * 提供现代化的确认对话框和操作引导
 */

class ConfirmSystem {
    constructor() {
        this.activeModal = null;
        this.init();
    }

    init() {
        this.addStyles();
    }

    addStyles() {
        if (document.getElementById('confirm-styles')) return;

        const style = document.createElement('style');
        style.id = 'confirm-styles';
        style.textContent = `
            .confirm-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                backdrop-filter: blur(4px);
                z-index: 10001;
                display: flex;
                align-items: center;
                justify-content: center;
                opacity: 0;
                transition: opacity 0.3s ease;
                padding: 20px;
            }

            .confirm-overlay.show {
                opacity: 1;
            }

            .confirm-modal {
                background: white;
                border-radius: 16px;
                box-shadow: 0 20px 80px rgba(0, 0, 0, 0.2);
                max-width: 480px;
                width: 100%;
                transform: scale(0.9) translateY(20px);
                transition: transform 0.3s ease;
                overflow: hidden;
            }

            .confirm-overlay.show .confirm-modal {
                transform: scale(1) translateY(0);
            }

            .confirm-header {
                padding: 24px 24px 16px;
                border-bottom: 1px solid #e9ecef;
            }

            .confirm-icon {
                width: 48px;
                height: 48px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 24px;
                margin: 0 auto 16px;
                color: white;
            }

            .confirm-icon.warning {
                background: linear-gradient(135deg, #ffc107, #ff8c00);
            }

            .confirm-icon.danger {
                background: linear-gradient(135deg, #dc3545, #c82333);
            }

            .confirm-icon.info {
                background: linear-gradient(135deg, #17a2b8, #138496);
            }

            .confirm-icon.success {
                background: linear-gradient(135deg, #28a745, #1e7e34);
            }

            .confirm-title {
                font-size: 20px;
                font-weight: 600;
                color: #212529;
                text-align: center;
                margin-bottom: 8px;
            }

            .confirm-subtitle {
                font-size: 14px;
                color: #6c757d;
                text-align: center;
                line-height: 1.4;
            }

            .confirm-body {
                padding: 16px 24px 24px;
            }

            .confirm-message {
                font-size: 15px;
                color: #495057;
                line-height: 1.5;
                text-align: center;
                margin-bottom: 20px;
            }

            .confirm-details {
                background: #f8f9fa;
                border-radius: 8px;
                padding: 16px;
                margin-bottom: 20px;
            }

            .confirm-detail-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid #e9ecef;
            }

            .confirm-detail-item:last-child {
                border-bottom: none;
            }

            .confirm-detail-label {
                font-weight: 500;
                color: #495057;
            }

            .confirm-detail-value {
                color: #212529;
            }

            .confirm-actions {
                display: flex;
                gap: 12px;
                justify-content: center;
            }

            .confirm-btn {
                padding: 12px 24px;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                min-width: 100px;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 8px;
            }

            .confirm-btn.secondary {
                background: white;
                color: #6c757d;
            }

            .confirm-btn.secondary:hover {
                background: #f8f9fa;
                border-color: #adb5bd;
            }

            .confirm-btn.primary {
                background: #007bff;
                border-color: #007bff;
                color: white;
            }

            .confirm-btn.primary:hover {
                background: #0056b3;
                border-color: #0056b3;
            }

            .confirm-btn.danger {
                background: #dc3545;
                border-color: #dc3545;
                color: white;
            }

            .confirm-btn.danger:hover {
                background: #c82333;
                border-color: #c82333;
            }

            .confirm-btn.warning {
                background: #ffc107;
                border-color: #ffc107;
                color: #212529;
            }

            .confirm-btn.warning:hover {
                background: #e0a800;
                border-color: #e0a800;
            }

            .confirm-input {
                width: 100%;
                padding: 12px;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                font-size: 14px;
                margin-bottom: 16px;
                transition: border-color 0.2s ease;
            }

            .confirm-input:focus {
                outline: none;
                border-color: #007bff;
                box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            }

            .confirm-checkbox {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 16px;
                font-size: 14px;
                color: #495057;
            }

            .confirm-checkbox input {
                width: 16px;
                height: 16px;
            }

            /* 移动端适配 */
            @media (max-width: 768px) {
                .confirm-modal {
                    margin: 20px;
                    max-width: none;
                }

                .confirm-actions {
                    flex-direction: column;
                }

                .confirm-btn {
                    width: 100%;
                }
            }

            /* 动画效果 */
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }

            .confirm-modal.shake {
                animation: shake 0.5s ease-in-out;
            }
        `;
        document.head.appendChild(style);
    }

    show(options) {
        return new Promise((resolve) => {
            const {
                type = 'info',
                title = '确认操作',
                subtitle = '',
                message = '您确定要执行此操作吗？',
                details = null,
                confirmText = '确认',
                cancelText = '取消',
                showCancel = true,
                requireInput = false,
                inputPlaceholder = '请输入确认信息',
                requireCheckbox = false,
                checkboxText = '我已阅读并同意',
                dangerous = false
            } = options;

            // 关闭现有模态框
            if (this.activeModal) {
                this.close();
            }

            const overlay = document.createElement('div');
            overlay.className = 'confirm-overlay';

            const iconMap = {
                info: 'bi-info-circle',
                warning: 'bi-exclamation-triangle',
                danger: 'bi-x-circle',
                success: 'bi-check-circle'
            };

            let detailsHtml = '';
            if (details && Array.isArray(details)) {
                const detailItems = details.map(item => 
                    `<div class="confirm-detail-item">
                        <span class="confirm-detail-label">${item.label}</span>
                        <span class="confirm-detail-value">${item.value}</span>
                    </div>`
                ).join('');
                detailsHtml = `<div class="confirm-details">${detailItems}</div>`;
            }

            let inputHtml = '';
            if (requireInput) {
                inputHtml = `<input type="text" class="confirm-input" placeholder="${inputPlaceholder}" required>`;
            }

            let checkboxHtml = '';
            if (requireCheckbox) {
                checkboxHtml = `
                    <label class="confirm-checkbox">
                        <input type="checkbox" required>
                        <span>${checkboxText}</span>
                    </label>
                `;
            }

            const confirmBtnClass = dangerous ? 'danger' : (type === 'warning' ? 'warning' : 'primary');

            overlay.innerHTML = `
                <div class="confirm-modal">
                    <div class="confirm-header">
                        <div class="confirm-icon ${type}">
                            <i class="bi ${iconMap[type]}"></i>
                        </div>
                        <div class="confirm-title">${title}</div>
                        ${subtitle ? `<div class="confirm-subtitle">${subtitle}</div>` : ''}
                    </div>
                    <div class="confirm-body">
                        <div class="confirm-message">${message}</div>
                        ${detailsHtml}
                        ${inputHtml}
                        ${checkboxHtml}
                        <div class="confirm-actions">
                            ${showCancel ? `<button class="confirm-btn secondary" data-action="cancel">${cancelText}</button>` : ''}
                            <button class="confirm-btn ${confirmBtnClass}" data-action="confirm">${confirmText}</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(overlay);
            this.activeModal = overlay;

            // 显示动画
            requestAnimationFrame(() => {
                overlay.classList.add('show');
            });

            // 绑定事件
            const modal = overlay.querySelector('.confirm-modal');
            const confirmBtn = overlay.querySelector('[data-action="confirm"]');
            const cancelBtn = overlay.querySelector('[data-action="cancel"]');
            const input = overlay.querySelector('.confirm-input');
            const checkbox = overlay.querySelector('.confirm-checkbox input');

            const validateAndConfirm = () => {
                let isValid = true;
                let inputValue = '';

                if (requireInput && input) {
                    inputValue = input.value.trim();
                    if (!inputValue) {
                        isValid = false;
                        input.focus();
                        modal.classList.add('shake');
                        setTimeout(() => modal.classList.remove('shake'), 500);
                    }
                }

                if (requireCheckbox && checkbox && !checkbox.checked) {
                    isValid = false;
                    modal.classList.add('shake');
                    setTimeout(() => modal.classList.remove('shake'), 500);
                }

                if (isValid) {
                    this.close();
                    resolve({ confirmed: true, inputValue });
                }
            };

            confirmBtn.addEventListener('click', validateAndConfirm);

            if (cancelBtn) {
                cancelBtn.addEventListener('click', () => {
                    this.close();
                    resolve({ confirmed: false });
                });
            }

            if (input) {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        validateAndConfirm();
                    }
                });
            }

            // 点击遮罩关闭
            overlay.addEventListener('click', (e) => {
                if (e.target === overlay) {
                    this.close();
                    resolve({ confirmed: false });
                }
            });

            // ESC键关闭
            const handleEscape = (e) => {
                if (e.key === 'Escape') {
                    this.close();
                    resolve({ confirmed: false });
                    document.removeEventListener('keydown', handleEscape);
                }
            };
            document.addEventListener('keydown', handleEscape);
        });
    }

    close() {
        if (!this.activeModal) return;

        this.activeModal.classList.remove('show');
        setTimeout(() => {
            if (this.activeModal && this.activeModal.parentNode) {
                this.activeModal.parentNode.removeChild(this.activeModal);
            }
            this.activeModal = null;
        }, 300);
    }

    // 便捷方法
    confirm(message, title = '确认操作') {
        return this.show({ type: 'info', title, message });
    }

    warning(message, title = '警告') {
        return this.show({ type: 'warning', title, message });
    }

    danger(message, title = '危险操作', confirmText = '确认删除') {
        return this.show({ 
            type: 'danger', 
            title, 
            message, 
            confirmText,
            dangerous: true 
        });
    }

    prompt(message, title = '请输入', placeholder = '请输入内容') {
        return this.show({
            type: 'info',
            title,
            message,
            requireInput: true,
            inputPlaceholder: placeholder
        });
    }
}

// 创建全局实例
window.confirm = new ConfirmSystem();

// 重写原生confirm
window.nativeConfirm = window.confirm;
window.confirm = function(message) {
    return window.confirm.confirm(message);
};
