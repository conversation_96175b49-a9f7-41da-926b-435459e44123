/**
 * 用户引导系统
 * 提供新手引导和操作提示
 */

class GuideSystem {
    constructor() {
        this.currentTour = null;
        this.currentStep = 0;
        this.init();
    }

    init() {
        this.addStyles();
    }

    addStyles() {
        if (document.getElementById('guide-styles')) return;

        const style = document.createElement('style');
        style.id = 'guide-styles';
        style.textContent = `
            .guide-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                z-index: 10002;
                opacity: 0;
                transition: opacity 0.3s ease;
                pointer-events: none;
            }

            .guide-overlay.show {
                opacity: 1;
                pointer-events: auto;
            }

            .guide-spotlight {
                position: absolute;
                border-radius: 8px;
                box-shadow: 0 0 0 9999px rgba(0, 0, 0, 0.7);
                transition: all 0.3s ease;
                pointer-events: none;
            }

            .guide-tooltip {
                position: absolute;
                background: white;
                border-radius: 12px;
                box-shadow: 0 16px 48px rgba(0, 0, 0, 0.2);
                padding: 0;
                max-width: 320px;
                min-width: 280px;
                z-index: 10003;
                opacity: 0;
                transform: scale(0.9);
                transition: all 0.3s ease;
            }

            .guide-tooltip.show {
                opacity: 1;
                transform: scale(1);
            }

            .guide-tooltip::before {
                content: '';
                position: absolute;
                width: 0;
                height: 0;
                border: 8px solid transparent;
            }

            .guide-tooltip.top::before {
                bottom: -16px;
                left: 50%;
                transform: translateX(-50%);
                border-top-color: white;
            }

            .guide-tooltip.bottom::before {
                top: -16px;
                left: 50%;
                transform: translateX(-50%);
                border-bottom-color: white;
            }

            .guide-tooltip.left::before {
                right: -16px;
                top: 50%;
                transform: translateY(-50%);
                border-left-color: white;
            }

            .guide-tooltip.right::before {
                left: -16px;
                top: 50%;
                transform: translateY(-50%);
                border-right-color: white;
            }

            .guide-header {
                padding: 20px 20px 16px;
                border-bottom: 1px solid #e9ecef;
            }

            .guide-step-indicator {
                display: flex;
                align-items: center;
                gap: 8px;
                margin-bottom: 12px;
                font-size: 12px;
                color: #6c757d;
            }

            .guide-step-number {
                background: #007bff;
                color: white;
                width: 20px;
                height: 20px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 11px;
                font-weight: 600;
            }

            .guide-title {
                font-size: 16px;
                font-weight: 600;
                color: #212529;
                margin: 0;
            }

            .guide-body {
                padding: 16px 20px 20px;
            }

            .guide-content {
                font-size: 14px;
                color: #495057;
                line-height: 1.5;
                margin-bottom: 16px;
            }

            .guide-actions {
                display: flex;
                justify-content: space-between;
                align-items: center;
                gap: 12px;
            }

            .guide-progress {
                display: flex;
                gap: 4px;
                flex: 1;
            }

            .guide-progress-dot {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background: #dee2e6;
                transition: background 0.2s ease;
            }

            .guide-progress-dot.active {
                background: #007bff;
            }

            .guide-progress-dot.completed {
                background: #28a745;
            }

            .guide-buttons {
                display: flex;
                gap: 8px;
            }

            .guide-btn {
                padding: 8px 16px;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                font-size: 13px;
                cursor: pointer;
                transition: all 0.2s ease;
                background: white;
                color: #495057;
            }

            .guide-btn:hover {
                background: #f8f9fa;
            }

            .guide-btn.primary {
                background: #007bff;
                border-color: #007bff;
                color: white;
            }

            .guide-btn.primary:hover {
                background: #0056b3;
            }

            .guide-btn:disabled {
                opacity: 0.5;
                cursor: not-allowed;
            }

            /* 高亮元素样式 */
            .guide-highlight {
                position: relative;
                z-index: 10001;
            }

            .guide-highlight::after {
                content: '';
                position: absolute;
                top: -4px;
                left: -4px;
                right: -4px;
                bottom: -4px;
                border: 2px solid #007bff;
                border-radius: 8px;
                animation: guide-pulse 2s infinite;
                pointer-events: none;
            }

            @keyframes guide-pulse {
                0% {
                    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4);
                }
                70% {
                    box-shadow: 0 0 0 10px rgba(0, 123, 255, 0);
                }
                100% {
                    box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
                }
            }

            /* 提示气泡 */
            .guide-hint {
                position: absolute;
                background: #007bff;
                color: white;
                padding: 8px 12px;
                border-radius: 20px;
                font-size: 12px;
                font-weight: 500;
                z-index: 1000;
                opacity: 0;
                transform: scale(0.8);
                transition: all 0.3s ease;
                pointer-events: none;
            }

            .guide-hint.show {
                opacity: 1;
                transform: scale(1);
            }

            .guide-hint::after {
                content: '';
                position: absolute;
                width: 0;
                height: 0;
                border: 6px solid transparent;
                border-top-color: #007bff;
                bottom: -12px;
                left: 50%;
                transform: translateX(-50%);
            }

            /* 移动端适配 */
            @media (max-width: 768px) {
                .guide-tooltip {
                    max-width: 280px;
                    min-width: 240px;
                }

                .guide-actions {
                    flex-direction: column;
                    align-items: stretch;
                }

                .guide-progress {
                    justify-content: center;
                    margin-bottom: 12px;
                }

                .guide-buttons {
                    width: 100%;
                }

                .guide-btn {
                    flex: 1;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 开始引导
    startTour(steps, options = {}) {
        const {
            onComplete = null,
            onSkip = null,
            showProgress = true,
            allowSkip = true
        } = options;

        this.currentTour = {
            steps,
            options: { onComplete, onSkip, showProgress, allowSkip },
            overlay: null,
            tooltip: null
        };
        this.currentStep = 0;

        this.createOverlay();
        this.showStep(0);
    }

    createOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'guide-overlay';
        document.body.appendChild(overlay);

        this.currentTour.overlay = overlay;

        requestAnimationFrame(() => {
            overlay.classList.add('show');
        });
    }

    showStep(stepIndex) {
        if (!this.currentTour || stepIndex >= this.currentTour.steps.length) {
            this.endTour();
            return;
        }

        this.currentStep = stepIndex;
        const step = this.currentTour.steps[stepIndex];

        // 移除之前的高亮
        this.clearHighlight();

        // 高亮目标元素
        const target = document.querySelector(step.target);
        if (target) {
            this.highlightElement(target);
            this.showTooltip(target, step);
        }
    }

    highlightElement(element) {
        element.classList.add('guide-highlight');
        
        // 滚动到元素位置
        element.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });

        // 创建聚光灯效果
        const rect = element.getBoundingClientRect();
        const spotlight = document.createElement('div');
        spotlight.className = 'guide-spotlight';
        spotlight.style.cssText = `
            top: ${rect.top - 8}px;
            left: ${rect.left - 8}px;
            width: ${rect.width + 16}px;
            height: ${rect.height + 16}px;
        `;

        this.currentTour.overlay.appendChild(spotlight);
    }

    showTooltip(target, step) {
        // 移除旧的tooltip
        if (this.currentTour.tooltip) {
            this.currentTour.tooltip.remove();
        }

        const tooltip = document.createElement('div');
        tooltip.className = 'guide-tooltip';

        const progressDots = this.currentTour.options.showProgress ? 
            this.currentTour.steps.map((_, index) => {
                let className = 'guide-progress-dot';
                if (index < this.currentStep) className += ' completed';
                else if (index === this.currentStep) className += ' active';
                return `<div class="${className}"></div>`;
            }).join('') : '';

        tooltip.innerHTML = `
            <div class="guide-header">
                <div class="guide-step-indicator">
                    <div class="guide-step-number">${this.currentStep + 1}</div>
                    <span>第 ${this.currentStep + 1} 步，共 ${this.currentTour.steps.length} 步</span>
                </div>
                <h3 class="guide-title">${step.title}</h3>
            </div>
            <div class="guide-body">
                <div class="guide-content">${step.content}</div>
                <div class="guide-actions">
                    <div class="guide-progress">${progressDots}</div>
                    <div class="guide-buttons">
                        ${this.currentTour.options.allowSkip ? '<button class="guide-btn" data-action="skip">跳过</button>' : ''}
                        ${this.currentStep > 0 ? '<button class="guide-btn" data-action="prev">上一步</button>' : ''}
                        <button class="guide-btn primary" data-action="next">
                            ${this.currentStep === this.currentTour.steps.length - 1 ? '完成' : '下一步'}
                        </button>
                    </div>
                </div>
            </div>
        `;

        this.currentTour.overlay.appendChild(tooltip);
        this.currentTour.tooltip = tooltip;

        // 定位tooltip
        this.positionTooltip(tooltip, target, step.position || 'bottom');

        // 绑定事件
        tooltip.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (action) {
                this.handleAction(action);
            }
        });

        // 显示动画
        requestAnimationFrame(() => {
            tooltip.classList.add('show');
        });
    }

    positionTooltip(tooltip, target, position) {
        const rect = target.getBoundingClientRect();
        const tooltipRect = tooltip.getBoundingClientRect();
        
        tooltip.className = `guide-tooltip ${position}`;

        let top, left;

        switch (position) {
            case 'top':
                top = rect.top - tooltipRect.height - 16;
                left = rect.left + (rect.width - tooltipRect.width) / 2;
                break;
            case 'bottom':
                top = rect.bottom + 16;
                left = rect.left + (rect.width - tooltipRect.width) / 2;
                break;
            case 'left':
                top = rect.top + (rect.height - tooltipRect.height) / 2;
                left = rect.left - tooltipRect.width - 16;
                break;
            case 'right':
                top = rect.top + (rect.height - tooltipRect.height) / 2;
                left = rect.right + 16;
                break;
        }

        // 确保tooltip在视窗内
        const margin = 20;
        top = Math.max(margin, Math.min(window.innerHeight - tooltipRect.height - margin, top));
        left = Math.max(margin, Math.min(window.innerWidth - tooltipRect.width - margin, left));

        tooltip.style.top = `${top}px`;
        tooltip.style.left = `${left}px`;
    }

    handleAction(action) {
        switch (action) {
            case 'next':
                if (this.currentStep === this.currentTour.steps.length - 1) {
                    this.endTour(true);
                } else {
                    this.showStep(this.currentStep + 1);
                }
                break;
            case 'prev':
                if (this.currentStep > 0) {
                    this.showStep(this.currentStep - 1);
                }
                break;
            case 'skip':
                this.endTour(false);
                break;
        }
    }

    clearHighlight() {
        document.querySelectorAll('.guide-highlight').forEach(el => {
            el.classList.remove('guide-highlight');
        });

        if (this.currentTour && this.currentTour.overlay) {
            const spotlight = this.currentTour.overlay.querySelector('.guide-spotlight');
            if (spotlight) {
                spotlight.remove();
            }
        }
    }

    endTour(completed = false) {
        this.clearHighlight();

        if (this.currentTour) {
            if (this.currentTour.overlay) {
                this.currentTour.overlay.classList.remove('show');
                setTimeout(() => {
                    if (this.currentTour.overlay.parentNode) {
                        this.currentTour.overlay.parentNode.removeChild(this.currentTour.overlay);
                    }
                }, 300);
            }

            const { onComplete, onSkip } = this.currentTour.options;
            if (completed && onComplete) {
                onComplete();
            } else if (!completed && onSkip) {
                onSkip();
            }
        }

        this.currentTour = null;
        this.currentStep = 0;
    }

    // 显示提示气泡
    showHint(target, text, duration = 3000) {
        const element = typeof target === 'string' ? document.querySelector(target) : target;
        if (!element) return;

        const hint = document.createElement('div');
        hint.className = 'guide-hint';
        hint.textContent = text;

        const rect = element.getBoundingClientRect();
        hint.style.cssText = `
            top: ${rect.top - 40}px;
            left: ${rect.left + rect.width / 2}px;
            transform: translateX(-50%);
        `;

        document.body.appendChild(hint);

        requestAnimationFrame(() => {
            hint.classList.add('show');
        });

        setTimeout(() => {
            hint.classList.remove('show');
            setTimeout(() => {
                if (hint.parentNode) {
                    hint.parentNode.removeChild(hint);
                }
            }, 300);
        }, duration);
    }
}

    // 显示提示气泡
    showHint(target, text, duration = 3000) {
        const element = typeof target === 'string' ? document.querySelector(target) : target;
        if (!element) return;

        const hint = document.createElement('div');
        hint.className = 'guide-hint';
        hint.textContent = text;

        const rect = element.getBoundingClientRect();
        hint.style.cssText = `
            top: ${rect.top - 40}px;
            left: ${rect.left + rect.width / 2}px;
            transform: translateX(-50%);
        `;

        document.body.appendChild(hint);

        requestAnimationFrame(() => {
            hint.classList.add('show');
        });

        setTimeout(() => {
            hint.classList.remove('show');
            setTimeout(() => {
                if (hint.parentNode) {
                    hint.parentNode.removeChild(hint);
                }
            }, 300);
        }, duration);
    }

    // 检查是否需要显示新手引导
    checkFirstVisit() {
        const hasVisited = localStorage.getItem('guide_completed');
        if (!hasVisited && window.location.pathname === '/') {
            this.showWelcomeTour();
        }
    }

    // 新手欢迎引导
    showWelcomeTour() {
        const steps = [
            {
                target: '.search-bar',
                title: '搜索商品',
                content: '在这里输入关键词搜索您需要的虚拟商品',
                position: 'bottom'
            },
            {
                target: '.category-nav',
                title: '浏览分类',
                content: '点击分类快速找到您感兴趣的商品类型',
                position: 'bottom'
            },
            {
                target: '.user-menu',
                title: '用户中心',
                content: '登录后可以发布商品、查看订单和管理账户',
                position: 'bottom'
            }
        ];

        this.startTour(steps, {
            onComplete: () => {
                localStorage.setItem('guide_completed', 'true');
                window.notify.success('欢迎使用', '您已完成新手引导！');
            },
            onSkip: () => {
                localStorage.setItem('guide_completed', 'true');
            }
        });
    }
}

// 创建全局实例
window.guide = new GuideSystem();
