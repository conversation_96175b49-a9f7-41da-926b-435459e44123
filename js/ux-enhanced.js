/**
 * 数字鱼平台 - 用户体验增强系统
 * 统一管理所有UX优化功能
 */

class UXEnhanced {
    constructor() {
        this.initialized = false;
        this.features = {
            notifications: true,
            loading: true,
            confirmations: true,
            guides: true,
            animations: true,
            accessibility: true
        };
        
        this.init();
    }

    async init() {
        if (this.initialized) return;

        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.setup());
        } else {
            this.setup();
        }
    }

    setup() {
        this.addGlobalStyles();
        this.enhanceButtons();
        this.enhanceForms();
        this.enhanceLinks();
        this.addKeyboardSupport();
        this.addTouchSupport();
        this.optimizeImages();
        this.addScrollEffects();
        this.setupErrorHandling();
        this.initialized = true;

        // 触发初始化完成事件
        this.emit('ux:initialized');
    }

    addGlobalStyles() {
        if (document.getElementById('ux-global-styles')) return;

        const style = document.createElement('style');
        style.id = 'ux-global-styles';
        style.textContent = `
            /* 全局UX增强样式 */
            * {
                box-sizing: border-box;
            }

            /* 平滑滚动 */
            html {
                scroll-behavior: smooth;
            }

            /* 焦点样式优化 */
            *:focus {
                outline: 2px solid #007bff;
                outline-offset: 2px;
            }

            /* 按钮悬停效果 */
            .btn, button, [role="button"] {
                transition: all 0.2s ease;
                cursor: pointer;
            }

            .btn:hover, button:hover, [role="button"]:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            }

            .btn:active, button:active, [role="button"]:active {
                transform: translateY(0);
            }

            /* 链接悬停效果 */
            a {
                transition: color 0.2s ease;
            }

            /* 输入框焦点效果 */
            input, textarea, select {
                transition: border-color 0.2s ease, box-shadow 0.2s ease;
            }

            input:focus, textarea:focus, select:focus {
                border-color: #007bff;
                box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
            }

            /* 卡片悬停效果 */
            .card, .product-card, .order-item {
                transition: transform 0.2s ease, box-shadow 0.2s ease;
            }

            .card:hover, .product-card:hover, .order-item:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            }

            /* 加载状态 */
            .loading {
                pointer-events: none;
                opacity: 0.7;
            }

            /* 错误状态 */
            .error {
                border-color: #dc3545 !important;
                box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1) !important;
            }

            /* 成功状态 */
            .success {
                border-color: #28a745 !important;
                box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.1) !important;
            }

            /* 无障碍优化 */
            .sr-only {
                position: absolute;
                width: 1px;
                height: 1px;
                padding: 0;
                margin: -1px;
                overflow: hidden;
                clip: rect(0, 0, 0, 0);
                white-space: nowrap;
                border: 0;
            }

            /* 减少动画（用户偏好） */
            @media (prefers-reduced-motion: reduce) {
                *, *::before, *::after {
                    animation-duration: 0.01ms !important;
                    animation-iteration-count: 1 !important;
                    transition-duration: 0.01ms !important;
                }
            }

            /* 高对比度模式 */
            @media (prefers-contrast: high) {
                .btn, button {
                    border: 2px solid currentColor;
                }
            }

            /* 暗色模式支持 */
            @media (prefers-color-scheme: dark) {
                :root {
                    --bg-color: #1a1a1a;
                    --text-color: #ffffff;
                    --border-color: #333333;
                }
            }
        `;
        document.head.appendChild(style);
    }

    enhanceButtons() {
        document.addEventListener('click', (e) => {
            const button = e.target.closest('button, .btn, [role="button"]');
            if (!button) return;

            // 添加点击波纹效果
            this.addRippleEffect(button, e);

            // 防止重复点击
            if (button.classList.contains('btn-loading')) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }

            // 表单提交按钮特殊处理
            if (button.type === 'submit') {
                const form = button.closest('form');
                if (form && !form.checkValidity()) {
                    this.highlightFormErrors(form);
                }
            }
        });
    }

    addRippleEffect(element, event) {
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        const ripple = document.createElement('span');
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
        `;

        // 添加动画样式
        if (!document.getElementById('ripple-animation')) {
            const style = document.createElement('style');
            style.id = 'ripple-animation';
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(2);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);

        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 600);
    }

    enhanceForms() {
        // 实时表单验证
        document.addEventListener('input', (e) => {
            if (e.target.matches('input, textarea, select')) {
                this.validateField(e.target);
            }
        });

        // 表单提交增强
        document.addEventListener('submit', (e) => {
            const form = e.target;
            if (!form.checkValidity()) {
                e.preventDefault();
                this.highlightFormErrors(form);
                window.notify.error('表单验证失败', '请检查并修正标红的字段');
            }
        });
    }

    validateField(field) {
        const isValid = field.checkValidity();
        
        field.classList.remove('error', 'success');
        
        if (field.value.trim()) {
            field.classList.add(isValid ? 'success' : 'error');
        }

        // 显示自定义错误消息
        if (!isValid && field.value.trim()) {
            this.showFieldError(field);
        }
    }

    showFieldError(field) {
        const existingError = field.parentNode.querySelector('.field-error');
        if (existingError) {
            existingError.remove();
        }

        const errorDiv = document.createElement('div');
        errorDiv.className = 'field-error';
        errorDiv.style.cssText = `
            color: #dc3545;
            font-size: 12px;
            margin-top: 4px;
            animation: fadeIn 0.3s ease;
        `;
        errorDiv.textContent = field.validationMessage;

        field.parentNode.appendChild(errorDiv);

        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.remove();
            }
        }, 5000);
    }

    highlightFormErrors(form) {
        const invalidFields = form.querySelectorAll(':invalid');
        invalidFields.forEach(field => {
            field.classList.add('error');
            this.showFieldError(field);
        });

        // 滚动到第一个错误字段
        if (invalidFields.length > 0) {
            invalidFields[0].scrollIntoView({
                behavior: 'smooth',
                block: 'center'
            });
            invalidFields[0].focus();
        }
    }

    enhanceLinks() {
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a');
            if (!link) return;

            // 外部链接处理
            if (link.hostname !== window.location.hostname) {
                link.target = '_blank';
                link.rel = 'noopener noreferrer';
            }

            // 添加加载状态
            if (link.href && !link.href.startsWith('javascript:')) {
                const loadingId = window.notify.loading('页面跳转中', '正在加载页面...');
                
                // 页面卸载时关闭加载提示
                window.addEventListener('beforeunload', () => {
                    window.notify.close(loadingId);
                }, { once: true });
            }
        });
    }

    addKeyboardSupport() {
        // 全局键盘快捷键
        document.addEventListener('keydown', (e) => {
            // Ctrl/Cmd + K 打开搜索
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                const searchInput = document.querySelector('.search-input, [name="search"]');
                if (searchInput) {
                    searchInput.focus();
                }
            }

            // ESC 关闭模态框
            if (e.key === 'Escape') {
                const modal = document.querySelector('.modal.show, .confirm-overlay.show');
                if (modal) {
                    const closeBtn = modal.querySelector('.close, .btn-cancel, [data-action="cancel"]');
                    if (closeBtn) {
                        closeBtn.click();
                    }
                }
            }
        });
    }

    addTouchSupport() {
        // 触摸设备优化
        if ('ontouchstart' in window) {
            document.body.classList.add('touch-device');
            
            // 添加触摸反馈
            document.addEventListener('touchstart', (e) => {
                const target = e.target.closest('button, .btn, a, [role="button"]');
                if (target) {
                    target.classList.add('touch-active');
                }
            });

            document.addEventListener('touchend', (e) => {
                const target = e.target.closest('button, .btn, a, [role="button"]');
                if (target) {
                    setTimeout(() => {
                        target.classList.remove('touch-active');
                    }, 150);
                }
            });
        }
    }

    optimizeImages() {
        // 图片懒加载
        const images = document.querySelectorAll('img[data-src]');
        if (images.length > 0 && 'IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                        imageObserver.unobserve(img);
                    }
                });
            });

            images.forEach(img => imageObserver.observe(img));
        }
    }

    addScrollEffects() {
        // 滚动到顶部按钮
        const scrollToTop = document.createElement('button');
        scrollToTop.className = 'scroll-to-top';
        scrollToTop.innerHTML = '<i class="bi bi-arrow-up"></i>';
        scrollToTop.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 48px;
            height: 48px;
            border: none;
            border-radius: 50%;
            background: #007bff;
            color: white;
            font-size: 18px;
            cursor: pointer;
            z-index: 1000;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        `;

        document.body.appendChild(scrollToTop);

        // 滚动监听
        window.addEventListener('scroll', () => {
            if (window.pageYOffset > 300) {
                scrollToTop.style.opacity = '1';
                scrollToTop.style.transform = 'translateY(0)';
            } else {
                scrollToTop.style.opacity = '0';
                scrollToTop.style.transform = 'translateY(20px)';
            }
        });

        scrollToTop.addEventListener('click', () => {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }

    setupErrorHandling() {
        // 全局错误处理
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
            window.notify.error('系统错误', '页面出现异常，请刷新重试');
        });

        // Promise错误处理
        window.addEventListener('unhandledrejection', (e) => {
            console.error('Unhandled promise rejection:', e.reason);
            window.notify.error('网络错误', '请求失败，请检查网络连接');
        });
    }

    // 事件系统
    emit(eventName, data = null) {
        const event = new CustomEvent(eventName, { detail: data });
        document.dispatchEvent(event);
    }

    on(eventName, callback) {
        document.addEventListener(eventName, callback);
    }

    // 性能监控
    measurePerformance() {
        if ('performance' in window) {
            window.addEventListener('load', () => {
                setTimeout(() => {
                    const perfData = performance.getEntriesByType('navigation')[0];
                    const loadTime = perfData.loadEventEnd - perfData.loadEventStart;
                    
                    if (loadTime > 3000) {
                        console.warn('Page load time is slow:', loadTime + 'ms');
                    }
                }, 0);
            });
        }
    }
}

// 初始化UX增强系统
window.ux = new UXEnhanced();
