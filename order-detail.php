<?php
// 订单详情页面
require_once 'includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录';
    redirect('login.php');
}

// 获取订单ID
$orderId = intval($_GET['id'] ?? 0);

if ($orderId <= 0) {
    $_SESSION['error_message'] = '订单ID无效';
    redirect('my-orders.php');
}

// 获取订单详细信息
try {
    $stmt = $pdo->prepare("
        SELECT o.*, p.title as product_title, p.images as product_images, p.price as product_price,
               p.description as product_description, p.is_virtual,
               u.nickname as seller_name, u.avatar as seller_avatar, u.phone as seller_phone,
               c.name as category_name
        FROM orders o
        JOIN products p ON o.product_id = p.id
        JOIN users u ON o.seller_id = u.id
        JOIN categories c ON p.category_id = c.id
        WHERE o.id = ? AND o.buyer_id = ?
    ");
    $stmt->execute([$orderId, $_SESSION['user_id']]);
    $order = $stmt->fetch();
    
    if (!$order) {
        $_SESSION['error_message'] = '订单不存在或无权限访问';
        redirect('my-orders.php');
    }
    
} catch (Exception $e) {
    $_SESSION['error_message'] = '获取订单信息失败';
    redirect('my-orders.php');
}

// 订单状态映射
$statusMap = [
    'pending' => ['label' => '待付款', 'class' => 'pending', 'icon' => 'clock'],
    'paid' => ['label' => '已付款', 'class' => 'paid', 'icon' => 'check-circle'],
    'shipped' => ['label' => '已发货', 'class' => 'shipped', 'icon' => 'truck'],
    'completed' => ['label' => '已完成', 'class' => 'completed', 'icon' => 'check-all'],
    'cancelled' => ['label' => '已取消', 'class' => 'cancelled', 'icon' => 'x-circle'],
    'refunding' => ['label' => '退款中', 'class' => 'refunding', 'icon' => 'arrow-counterclockwise'],
    'refunded' => ['label' => '已退款', 'class' => 'refunded', 'icon' => 'arrow-counterclockwise']
];

$pageTitle = '订单详情';
$additionalCSS = ['css/member.css', 'css/order-detail.css'];
require_once 'includes/header.php';
?>

<main class="main member-page">
    <div class="container">
        <div class="member-container">
            <!-- 侧边栏 -->
            <?php include 'includes/member-sidebar.php'; ?>

            <!-- 主内容区 -->
            <div class="member-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">订单详情</h1>
                        <div class="breadcrumb">
                            <a href="my-orders.php">我的订单</a>
                            <i class="bi bi-chevron-right"></i>
                            <span>订单详情</span>
                        </div>
                    </div>
                </div>

                <!-- 订单状态 -->
                <div class="order-status-section">
                    <div class="status-card">
                        <div class="status-icon status-<?php echo $order['status']; ?>">
                            <i class="bi bi-<?php echo $statusMap[$order['status']]['icon']; ?>"></i>
                        </div>
                        <div class="status-info">
                            <h2 class="status-title"><?php echo $statusMap[$order['status']]['label']; ?></h2>
                            <p class="status-desc">
                                <?php
                                switch ($order['status']) {
                                    case 'pending':
                                        echo '订单已创建，请尽快完成支付';
                                        break;
                                    case 'paid':
                                        echo '支付成功，卖家正在准备发货';
                                        break;
                                    case 'shipped':
                                        echo '商品已发货，请注意查收';
                                        break;
                                    case 'completed':
                                        echo '订单已完成，感谢您的购买';
                                        break;
                                    case 'cancelled':
                                        echo '订单已取消';
                                        break;
                                    case 'refunding':
                                        echo '退款申请处理中，请耐心等待';
                                        break;
                                    case 'refunded':
                                        echo '退款已完成';
                                        break;
                                }
                                ?>
                            </p>
                        </div>
                        <div class="order-time">
                            <span class="time-label">下单时间</span>
                            <span class="time-value"><?php echo date('Y-m-d H:i:s', strtotime($order['created_at'])); ?></span>
                        </div>
                    </div>
                </div>

                <!-- 订单信息 -->
                <div class="order-info-section">
                    <h3 class="section-title">订单信息</h3>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">订单号</span>
                            <span class="info-value"><?php echo $order['order_number'] ?? 'XY' . date('YmdHis', strtotime($order['created_at'])) . str_pad($order['id'], 4, '0', STR_PAD_LEFT); ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">商品类型</span>
                            <span class="info-value">
                                <?php echo $order['is_virtual'] ? '虚拟商品' : '实物商品'; ?>
                                <span class="category-tag"><?php echo htmlspecialchars($order['category_name']); ?></span>
                            </span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">支付方式</span>
                            <span class="info-value"><?php echo $order['payment_method'] ?: '待选择'; ?></span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">订单状态</span>
                            <span class="info-value status-<?php echo $order['status']; ?>">
                                <?php echo $statusMap[$order['status']]['label']; ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 商品信息 -->
                <div class="product-info-section">
                    <h3 class="section-title">商品信息</h3>
                    <div class="product-card">
                        <div class="product-image">
                            <?php
                            $images = json_decode($order['product_images'], true);
                            $firstImage = $images ? (UPLOAD_PATH . $images[0]) : 'images/product-default.svg';
                            ?>
                            <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($order['product_title']); ?>">
                        </div>
                        <div class="product-details">
                            <h4 class="product-title">
                                <a href="detail.php?id=<?php echo $order['product_id']; ?>">
                                    <?php echo htmlspecialchars($order['product_title']); ?>
                                </a>
                            </h4>
                            <div class="product-desc"><?php echo nl2br(htmlspecialchars(substr($order['product_description'], 0, 200))); ?>...</div>
                            <div class="product-meta">
                                <span class="quantity">数量：<?php echo $order['quantity']; ?></span>
                                <span class="unit-price">单价：<?php echo formatPrice($order['product_price']); ?></span>
                            </div>
                        </div>
                        <div class="product-price">
                            <div class="total-price"><?php echo formatPrice($order['total_price']); ?></div>
                        </div>
                    </div>
                </div>

                <!-- 卖家信息 -->
                <div class="seller-info-section">
                    <h3 class="section-title">卖家信息</h3>
                    <div class="seller-card">
                        <div class="seller-avatar">
                            <img src="<?php echo $order['seller_avatar'] ?: 'images/avatar-default.svg'; ?>" alt="卖家头像">
                        </div>
                        <div class="seller-details">
                            <h4 class="seller-name"><?php echo htmlspecialchars($order['seller_name']); ?></h4>
                            <div class="seller-contact">
                                <span class="contact-item">
                                    <i class="bi bi-telephone"></i>
                                    <?php echo $order['seller_phone'] ? substr($order['seller_phone'], 0, 3) . '****' . substr($order['seller_phone'], -4) : '未提供'; ?>
                                </span>
                            </div>
                        </div>
                        <div class="seller-actions">
                            <button class="btn-contact" onclick="contactSeller(<?php echo $order['seller_id']; ?>)">
                                <i class="bi bi-chat-dots"></i>
                                联系卖家
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 订单操作 -->
                <div class="order-actions-section">
                    <div class="actions-container">
                        <?php if ($order['status'] === 'pending'): ?>
                            <a href="pay.php?order_id=<?php echo $order['id']; ?>" class="action-btn primary">
                                <i class="bi bi-credit-card"></i>
                                立即付款
                            </a>
                            <button class="action-btn secondary" onclick="cancelOrder(<?php echo $order['id']; ?>)">
                                <i class="bi bi-x-circle"></i>
                                取消订单
                            </button>
                        <?php elseif ($order['status'] === 'paid'): ?>
                            <button class="action-btn success" onclick="confirmReceived(<?php echo $order['id']; ?>)">
                                <i class="bi bi-check-circle"></i>
                                确认收货
                            </button>
                            <button class="action-btn warning" onclick="requestRefund(<?php echo $order['id']; ?>)">
                                <i class="bi bi-arrow-counterclockwise"></i>
                                申请退款
                            </button>
                        <?php elseif ($order['status'] === 'completed'): ?>
                            <a href="review.php?order_id=<?php echo $order['id']; ?>" class="action-btn info">
                                <i class="bi bi-star"></i>
                                评价商品
                            </a>
                            <button class="action-btn warning" onclick="requestRefund(<?php echo $order['id']; ?>)">
                                <i class="bi bi-arrow-counterclockwise"></i>
                                申请退款
                            </button>
                        <?php elseif ($order['status'] === 'cancelled'): ?>
                            <a href="detail.php?id=<?php echo $order['product_id']; ?>" class="action-btn primary">
                                <i class="bi bi-cart-plus"></i>
                                再次购买
                            </a>
                        <?php endif; ?>
                        
                        <a href="my-orders.php" class="action-btn">
                            <i class="bi bi-arrow-left"></i>
                            返回订单列表
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
// 引用my-orders.php中的函数
function cancelOrder(orderId) {
    if (confirm('确定要取消这个订单吗？取消后将恢复商品库存。')) {
        const formData = new FormData();
        formData.append('action', 'cancel');
        formData.append('order_id', orderId);
        
        fetch('api/order-actions.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('订单已取消', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showMessage(data.message || '取消订单失败', 'error');
            }
        })
        .catch(error => {
            console.error('Cancel order error:', error);
            showMessage('网络错误，请重试', 'error');
        });
    }
}

function confirmReceived(orderId) {
    if (confirm('确认已收到商品？确认后订单将完成，无法撤销。')) {
        const formData = new FormData();
        formData.append('action', 'confirm_received');
        formData.append('order_id', orderId);
        
        fetch('api/order-actions.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('确认收货成功', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showMessage(data.message || '确认收货失败', 'error');
            }
        })
        .catch(error => {
            console.error('Confirm received error:', error);
            showMessage('网络错误，请重试', 'error');
        });
    }
}

function requestRefund(orderId) {
    if (confirm('确定要申请退款吗？我们将在1-3个工作日内处理您的申请。')) {
        const formData = new FormData();
        formData.append('action', 'request_refund');
        formData.append('order_id', orderId);
        
        fetch('api/order-actions.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('退款申请已提交', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showMessage(data.message || '申请退款失败', 'error');
            }
        })
        .catch(error => {
            console.error('Request refund error:', error);
            showMessage('网络错误，请重试', 'error');
        });
    }
}

function contactSeller(sellerId) {
    showMessage('联系卖家功能开发中...', 'info');
}

function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-toast message-${type}`;
    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);
    
    setTimeout(() => messageDiv.classList.add('show'), 100);
    setTimeout(() => {
        messageDiv.classList.remove('show');
        setTimeout(() => messageDiv.remove(), 300);
    }, 3000);
}
</script>

<style>
/* 订单详情页面样式 */
.order-status-section {
    margin-bottom: 30px;
}

.status-card {
    background: white;
    padding: 25px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    gap: 20px;
}

.status-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
}

.status-icon.status-pending {
    background: linear-gradient(135deg, #ffc107, #ffca2c);
}

.status-icon.status-paid {
    background: linear-gradient(135deg, #17a2b8, #20c997);
}

.status-icon.status-shipped {
    background: linear-gradient(135deg, #6f42c1, #8e44ad);
}

.status-icon.status-completed {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.status-icon.status-cancelled {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.status-icon.status-refunding,
.status-icon.status-refunded {
    background: linear-gradient(135deg, #fd7e14, #ff8c42);
}

.status-info {
    flex: 1;
}

.status-title {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin: 0 0 5px 0;
}

.status-desc {
    color: #666;
    margin: 0;
    font-size: 14px;
}

.order-time {
    text-align: right;
}

.time-label {
    display: block;
    font-size: 12px;
    color: #999;
    margin-bottom: 2px;
}

.time-value {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

/* 信息网格 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.info-item {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.info-label {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.info-value {
    font-size: 16px;
    color: #333;
    font-weight: 600;
}

.category-tag {
    display: inline-block;
    background: #e3f2fd;
    color: #1976d2;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    margin-left: 8px;
}

/* 商品卡片 */
.product-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.product-image {
    width: 120px;
    height: 120px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-details {
    flex: 1;
}

.product-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
    line-height: 1.4;
}

.product-title a {
    color: inherit;
    text-decoration: none;
}

.product-title a:hover {
    color: #ff6b35;
}

.product-desc {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 15px;
}

.product-meta {
    display: flex;
    gap: 20px;
    font-size: 14px;
    color: #666;
}

.product-price {
    text-align: right;
}

.total-price {
    font-size: 20px;
    font-weight: 600;
    color: #ff6b35;
}

/* 卖家卡片 */
.seller-card {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: flex;
    align-items: center;
    gap: 15px;
}

.seller-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    flex-shrink: 0;
}

.seller-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.seller-details {
    flex: 1;
}

.seller-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 8px 0;
}

.seller-contact {
    display: flex;
    gap: 15px;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: #666;
}

.btn-contact {
    background: linear-gradient(135deg, #17a2b8, #20c997);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
    transition: all 0.3s ease;
}

.btn-contact:hover {
    background: linear-gradient(135deg, #138496, #1e7e34);
    transform: translateY(-1px);
}

/* 订单操作区 */
.order-actions-section {
    margin-top: 30px;
}

.actions-container {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .status-card {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .product-card {
        flex-direction: column;
        text-align: center;
    }

    .product-image {
        align-self: center;
    }

    .seller-card {
        flex-direction: column;
        text-align: center;
    }

    .actions-container {
        flex-direction: column;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
