<?php
// 先处理所有逻辑，再包含header
require_once 'includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录';
    redirect('login.php');
}

$currentUser = getCurrentUser();

// 获取筛选参数
$status = $_GET['status'] ?? 'all';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

// 构建查询条件
$whereClause = "WHERE o.buyer_id = ?";
$params = [$_SESSION['user_id']];

if ($status !== 'all') {
    $whereClause .= " AND o.status = ?";
    $params[] = $status;
}

// 获取用户订单
try {
    $stmt = $pdo->prepare("
        SELECT o.*, p.title as product_title, p.images as product_images, p.price as product_price,
               u.nickname as seller_name, u.avatar as seller_avatar
        FROM orders o
        JOIN products p ON o.product_id = p.id
        JOIN users u ON o.seller_id = u.id
        $whereClause
        ORDER BY o.created_at DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([...$params, $limit, $offset]);
    $orders = $stmt->fetchAll();
    
    // 获取总数
    $countStmt = $pdo->prepare("SELECT COUNT(*) FROM orders o $whereClause");
    $countStmt->execute($params);
    $totalOrders = $countStmt->fetchColumn();
    $totalPages = ceil($totalOrders / $limit);
    
} catch (Exception $e) {
    $orders = [];
    $totalOrders = 0;
    $totalPages = 0;
}

// 获取订单统计
try {
    $statsStmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total,
            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
            SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END) as paid,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed,
            SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled,
            SUM(CASE WHEN status = 'refunded' THEN 1 ELSE 0 END) as refunded
        FROM orders
        WHERE buyer_id = ?
    ");
    $statsStmt->execute([$_SESSION['user_id']]);
    $stats = $statsStmt->fetch();
} catch (Exception $e) {
    $stats = [
        'total' => 0,
        'pending' => 0,
        'paid' => 0,
        'completed' => 0,
        'cancelled' => 0,
        'refunded' => 0
    ];
}

// 订单状态映射
$statusMap = [
    'pending' => ['label' => '待付款', 'class' => 'pending'],
    'paid' => ['label' => '已付款', 'class' => 'paid'],
    'shipped' => ['label' => '已发货', 'class' => 'shipped'],
    'completed' => ['label' => '已完成', 'class' => 'completed'],
    'cancelled' => ['label' => '已取消', 'class' => 'cancelled'],
    'refunding' => ['label' => '退款中', 'class' => 'refunding'],
    'refunded' => ['label' => '已退款', 'class' => 'refunded']
];

// 现在包含header，因为所有逻辑处理已完成
$pageTitle = '我的订单';
$additionalCSS = ['css/member.css', 'css/orders.css'];
require_once 'includes/header.php';
?>

<!-- 主要内容区 -->
<main class="main member-page">
    <div class="container">
        <div class="member-container">
            <!-- 侧边栏 -->
            <?php include 'includes/member-sidebar.php'; ?>

            <!-- 主内容区 -->
            <div class="member-content">
                <!-- 页面标题 -->
                <div class="page-header">
                    <div class="page-title-section">
                        <h1 class="page-title">我的订单</h1>
                        <p class="page-subtitle">查看和管理您的所有订单</p>
                    </div>
                </div>

            <!-- 订单统计 -->
            <div class="order-stats">
                <div class="stat-item">
                    <div class="stat-icon pending">
                        <i class="bi bi-clock"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo $stats['pending']; ?></div>
                        <div class="stat-label">待付款</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon paid">
                        <i class="bi bi-check-circle"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo $stats['paid']; ?></div>
                        <div class="stat-label">已付款</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon completed">
                        <i class="bi bi-check-all"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo $stats['completed']; ?></div>
                        <div class="stat-label">已完成</div>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon refunded">
                        <i class="bi bi-arrow-counterclockwise"></i>
                    </div>
                    <div class="stat-content">
                        <div class="stat-value"><?php echo $stats['refunded']; ?></div>
                        <div class="stat-label">退款/售后</div>
                    </div>
                </div>
            </div>

            <!-- 筛选标签 -->
            <div class="filter-tabs">
                <a href="?status=all" class="filter-tab <?php echo $status === 'all' ? 'active' : ''; ?>">
                    全部 (<?php echo $stats['total']; ?>)
                </a>
                <a href="?status=pending" class="filter-tab <?php echo $status === 'pending' ? 'active' : ''; ?>">
                    待付款 (<?php echo $stats['pending']; ?>)
                </a>
                <a href="?status=paid" class="filter-tab <?php echo $status === 'paid' ? 'active' : ''; ?>">
                    已付款 (<?php echo $stats['paid']; ?>)
                </a>
                <a href="?status=completed" class="filter-tab <?php echo $status === 'completed' ? 'active' : ''; ?>">
                    已完成 (<?php echo $stats['completed']; ?>)
                </a>
                <a href="?status=refunded" class="filter-tab <?php echo $status === 'refunded' ? 'active' : ''; ?>">
                    退款/售后 (<?php echo $stats['refunded']; ?>)
                </a>
            </div>

            <!-- 订单列表 -->
            <div class="orders-section">
                <?php if (empty($orders)): ?>
                    <div class="empty-state">
                        <div class="empty-icon">
                            <i class="bi bi-bag"></i>
                        </div>
                        <h3>还没有任何订单</h3>
                        <p>去逛逛，发现更多好商品吧！</p>
                        <a href="index.php" class="btn btn-primary">去逛逛</a>
                    </div>
                <?php else: ?>
                    <div class="orders-list">
                        <?php foreach ($orders as $order): ?>
                            <div class="order-card">
                                <div class="order-header">
                                    <div class="order-info">
                                        <span class="order-number">订单号：<?php echo $order['order_number'] ?? 'XY' . date('YmdHis', strtotime($order['created_at'])) . str_pad($order['id'], 4, '0', STR_PAD_LEFT); ?></span>
                                        <span class="order-time"><?php echo date('Y-m-d H:i', strtotime($order['created_at'])); ?></span>
                                    </div>
                                    <div class="order-status status-<?php echo $order['status']; ?>">
                                        <?php echo $statusMap[$order['status']]['label']; ?>
                                    </div>
                                </div>
                                
                                <div class="order-content">
                                    <div class="product-info">
                                        <div class="product-image">
                                            <?php
                                            $images = json_decode($order['product_images'], true);
                                            $firstImage = $images ? (UPLOAD_PATH . $images[0]) : 'images/product-default.svg';
                                            ?>
                                            <img src="<?php echo $firstImage; ?>" alt="<?php echo htmlspecialchars($order['product_title']); ?>">
                                        </div>
                                        <div class="product-details">
                                            <h3 class="product-title">
                                                <a href="detail.php?id=<?php echo $order['product_id']; ?>">
                                                    <?php echo htmlspecialchars($order['product_title']); ?>
                                                </a>
                                            </h3>
                                            <div class="seller-info">
                                                <img src="<?php echo $order['seller_avatar'] ?: 'images/avatar-default.svg'; ?>" alt="卖家头像" class="seller-avatar">
                                                <span class="seller-name"><?php echo htmlspecialchars($order['seller_name']); ?></span>
                                            </div>
                                            <div class="order-quantity">数量：<?php echo $order['quantity']; ?></div>
                                        </div>
                                    </div>
                                    
                                    <div class="order-price">
                                        <div class="total-price"><?php echo formatPrice($order['total_price']); ?></div>
                                        <div class="unit-price">单价：<?php echo formatPrice($order['product_price']); ?></div>
                                    </div>
                                </div>
                                
                                <div class="order-actions">
                                    <?php if ($order['status'] === 'pending'): ?>
                                        <a href="pay.php?order_id=<?php echo $order['id']; ?>" class="action-btn primary">
                                            <i class="bi bi-credit-card"></i>
                                            立即付款
                                        </a>
                                        <button class="action-btn secondary" onclick="cancelOrder(<?php echo $order['id']; ?>)">
                                            <i class="bi bi-x-circle"></i>
                                            取消订单
                                        </button>
                                    <?php elseif ($order['status'] === 'paid'): ?>
                                        <a href="order-detail.php?id=<?php echo $order['id']; ?>" class="action-btn">
                                            <i class="bi bi-eye"></i>
                                            查看详情
                                        </a>
                                        <button class="action-btn success" onclick="confirmReceived(<?php echo $order['id']; ?>)">
                                            <i class="bi bi-check-circle"></i>
                                            确认收货
                                        </button>
                                        <button class="action-btn warning" onclick="requestRefund(<?php echo $order['id']; ?>)">
                                            <i class="bi bi-arrow-counterclockwise"></i>
                                            申请退款
                                        </button>
                                    <?php elseif ($order['status'] === 'completed'): ?>
                                        <a href="order-detail.php?id=<?php echo $order['id']; ?>" class="action-btn">
                                            <i class="bi bi-eye"></i>
                                            查看详情
                                        </a>
                                        <a href="review.php?order_id=<?php echo $order['id']; ?>" class="action-btn info">
                                            <i class="bi bi-star"></i>
                                            评价
                                        </a>
                                        <button class="action-btn warning" onclick="requestRefund(<?php echo $order['id']; ?>)">
                                            <i class="bi bi-arrow-counterclockwise"></i>
                                            申请退款
                                        </button>
                                    <?php elseif ($order['status'] === 'cancelled'): ?>
                                        <a href="order-detail.php?id=<?php echo $order['id']; ?>" class="action-btn">
                                            <i class="bi bi-eye"></i>
                                            查看详情
                                        </a>
                                        <a href="detail.php?id=<?php echo $order['product_id']; ?>" class="action-btn primary">
                                            <i class="bi bi-cart-plus"></i>
                                            再次购买
                                        </a>
                                    <?php elseif ($order['status'] === 'refunded'): ?>
                                        <a href="order-detail.php?id=<?php echo $order['id']; ?>" class="action-btn">
                                            <i class="bi bi-eye"></i>
                                            查看详情
                                        </a>
                                    <?php else: ?>
                                        <a href="order-detail.php?id=<?php echo $order['id']; ?>" class="action-btn">
                                            <i class="bi bi-eye"></i>
                                            查看详情
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- 分页 -->
                    <?php if ($totalPages > 1): ?>
                        <div class="pagination">
                            <?php if ($page > 1): ?>
                                <a href="?status=<?php echo $status; ?>&page=<?php echo $page - 1; ?>" class="page-btn">
                                    <i class="bi bi-chevron-left"></i>
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <a href="?status=<?php echo $status; ?>&page=<?php echo $i; ?>" 
                                   class="page-btn <?php echo $i === $page ? 'active' : ''; ?>">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <a href="?status=<?php echo $status; ?>&page=<?php echo $page + 1; ?>" class="page-btn">
                                    <i class="bi bi-chevron-right"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
// 取消订单
function cancelOrder(orderId) {
    if (confirm('确定要取消这个订单吗？取消后将恢复商品库存。')) {
        const formData = new FormData();
        formData.append('action', 'cancel');
        formData.append('order_id', orderId);

        fetch('api/order-actions.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('订单已取消', 'success');
                // 刷新页面或更新订单状态
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showMessage(data.message || '取消订单失败', 'error');
            }
        })
        .catch(error => {
            console.error('Cancel order error:', error);
            showMessage('网络错误，请重试', 'error');
        });
    }
}

// 确认收货
function confirmReceived(orderId) {
    if (confirm('确认已收到商品？确认后订单将完成，无法撤销。')) {
        const formData = new FormData();
        formData.append('action', 'confirm_received');
        formData.append('order_id', orderId);

        fetch('api/order-actions.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('确认收货成功', 'success');
                // 刷新页面或更新订单状态
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showMessage(data.message || '确认收货失败', 'error');
            }
        })
        .catch(error => {
            console.error('Confirm received error:', error);
            showMessage('网络错误，请重试', 'error');
        });
    }
}

// 申请退款
function requestRefund(orderId) {
    if (confirm('确定要申请退款吗？我们将在1-3个工作日内处理您的申请。')) {
        const formData = new FormData();
        formData.append('action', 'request_refund');
        formData.append('order_id', orderId);

        fetch('api/order-actions.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showMessage('退款申请已提交', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1500);
            } else {
                showMessage(data.message || '申请退款失败', 'error');
            }
        })
        .catch(error => {
            console.error('Request refund error:', error);
            showMessage('网络错误，请重试', 'error');
        });
    }
}

// 消息提示函数
function showMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message-toast message-${type}`;
    messageDiv.textContent = message;
    document.body.appendChild(messageDiv);

    setTimeout(() => messageDiv.classList.add('show'), 100);
    setTimeout(() => {
        messageDiv.classList.remove('show');
        setTimeout(() => messageDiv.remove(), 300);
    }, 3000);
}
</script>

<style>
/* 订单操作按钮样式增强 */
.order-actions {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    justify-content: flex-end;
    margin-top: 15px;
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 6px;
    background: white;
    color: #666;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-btn.primary {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    color: white;
    border-color: #ff6b35;
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, #ff5722, #ff6b35);
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.action-btn.secondary {
    background: #f8f9fa;
    color: #6c757d;
    border-color: #dee2e6;
}

.action-btn.secondary:hover {
    background: #e9ecef;
    color: #495057;
}

.action-btn.success {
    background: #28a745;
    color: white;
    border-color: #28a745;
}

.action-btn.success:hover {
    background: #218838;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
}

.action-btn.warning {
    background: #ffc107;
    color: #212529;
    border-color: #ffc107;
}

.action-btn.warning:hover {
    background: #e0a800;
    box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.action-btn.info {
    background: #17a2b8;
    color: white;
    border-color: #17a2b8;
}

.action-btn.info:hover {
    background: #138496;
    box-shadow: 0 4px 15px rgba(23, 162, 184, 0.3);
}

.action-btn i {
    font-size: 14px;
}

/* 消息提示样式 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.message-toast.show {
    transform: translateX(0);
}

.message-toast.message-success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.message-toast.message-error {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
}

.message-toast.message-info {
    background: linear-gradient(135deg, #17a2b8, #3498db);
}

/* 响应式优化 */
@media (max-width: 768px) {
    .order-actions {
        justify-content: center;
        gap: 6px;
    }

    .action-btn {
        padding: 6px 12px;
        font-size: 13px;
    }

    .action-btn i {
        font-size: 13px;
    }
}
</style>

<?php require_once 'includes/footer.php'; ?>
