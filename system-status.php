<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统状态检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .status-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        .status-item:last-child {
            border-bottom: none;
        }
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .status-warning {
            color: #ffc107;
            font-weight: bold;
        }
        .btn {
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="status-container">
        <h1>🔍 系统状态检查</h1>
        <p>检查登录注册系统的各项配置和状态</p>
    </div>

    <div class="status-container">
        <h2>📁 文件检查</h2>
        <div class="status-item">
            <span>登录页面 (login.php)</span>
            <span class="<?php echo file_exists('login.php') ? 'status-ok' : 'status-error'; ?>">
                <?php echo file_exists('login.php') ? '✅ 存在' : '❌ 缺失'; ?>
            </span>
        </div>
        <div class="status-item">
            <span>注册页面 (register.php)</span>
            <span class="<?php echo file_exists('register.php') ? 'status-ok' : 'status-error'; ?>">
                <?php echo file_exists('register.php') ? '✅ 存在' : '❌ 缺失'; ?>
            </span>
        </div>
        <div class="status-item">
            <span>登录API (api/login.php)</span>
            <span class="<?php echo file_exists('api/login.php') ? 'status-ok' : 'status-error'; ?>">
                <?php echo file_exists('api/login.php') ? '✅ 存在' : '❌ 缺失'; ?>
            </span>
        </div>
        <div class="status-item">
            <span>注册API (api/register.php)</span>
            <span class="<?php echo file_exists('api/register.php') ? 'status-ok' : 'status-error'; ?>">
                <?php echo file_exists('api/register.php') ? '✅ 存在' : '❌ 缺失'; ?>
            </span>
        </div>
        <div class="status-item">
            <span>配置文件 (includes/config.php)</span>
            <span class="<?php echo file_exists('includes/config.php') ? 'status-ok' : 'status-error'; ?>">
                <?php echo file_exists('includes/config.php') ? '✅ 存在' : '❌ 缺失'; ?>
            </span>
        </div>
    </div>

    <div class="status-container">
        <h2>🗄️ 数据库检查</h2>
        <?php
        try {
            require_once 'includes/config.php';
            $pdo = new PDO($dsn, $username_db, $password_db, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
            ]);
            
            echo '<div class="status-item"><span>数据库连接</span><span class="status-ok">✅ 正常</span></div>';
            
            // 检查必要的表
            $requiredTables = ['users', 'categories', 'products'];
            foreach ($requiredTables as $table) {
                $stmt = $pdo->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                $exists = $stmt->fetch();
                
                echo '<div class="status-item">';
                echo '<span>数据表: ' . $table . '</span>';
                echo '<span class="' . ($exists ? 'status-ok' : 'status-error') . '">';
                echo $exists ? '✅ 存在' : '❌ 缺失';
                echo '</span>';
                echo '</div>';
            }
            
            // 检查测试用户
            $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE username IN ('testuser', 'admin')");
            $stmt->execute();
            $userCount = $stmt->fetch()['count'];
            
            echo '<div class="status-item">';
            echo '<span>测试用户</span>';
            echo '<span class="' . ($userCount > 0 ? 'status-ok' : 'status-warning') . '">';
            echo $userCount > 0 ? '✅ 已创建 (' . $userCount . '个)' : '⚠️ 未创建';
            echo '</span>';
            echo '</div>';
            
        } catch (Exception $e) {
            echo '<div class="status-item"><span>数据库连接</span><span class="status-error">❌ 失败: ' . $e->getMessage() . '</span></div>';
        }
        ?>
    </div>

    <div class="status-container">
        <h2>🔧 快速操作</h2>
        <div style="margin-bottom: 10px;">
            <a href="api/fix-database.php" class="btn btn-warning">🔧 修复数据库</a>
            <small style="display:block;margin-top:5px;color:#666;">修复现有表结构，添加缺失字段</small>
        </div>
        <div style="margin-bottom: 10px;">
            <a href="api/create-auth-tables.php" class="btn btn-success">创建数据表和测试用户</a>
        </div>
        <div style="margin-bottom: 10px;">
            <a href="simple-login-test.php" class="btn">简单登录测试</a>
        </div>
        <div style="margin-bottom: 10px;">
            <a href="test-auth.php" class="btn">完整功能测试</a>
        </div>
        <div style="margin-bottom: 10px;">
            <a href="login.php" class="btn">正式登录页面</a>
        </div>
    </div>

    <div class="status-container">
        <h2>📋 测试账号信息</h2>
        <pre>
普通用户:
用户名: testuser
邮箱: <EMAIL>
手机: 13800138000
密码: 123456

管理员:
用户名: admin
邮箱: <EMAIL>
手机: 13800138001
密码: admin123

短信验证码 (开发环境): 123456
        </pre>
    </div>

    <div class="status-container">
        <h2>🐛 故障排除</h2>
        <div class="status-item">
            <span>如果登录失败</span>
            <span>1. 检查数据库连接 2. 确保用户表存在 3. 检查测试用户是否创建</span>
        </div>
        <div class="status-item">
            <span>如果出现JavaScript错误</span>
            <span>1. 检查浏览器控制台 2. 确保API返回正确JSON 3. 检查网络请求</span>
        </div>
        <div class="status-item">
            <span>如果页面样式异常</span>
            <span>1. 检查CSS文件 2. 清除浏览器缓存 3. 检查文件路径</span>
        </div>
    </div>
</body>
</html>
