<?php
session_start();
require_once '../config/database.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

// 获取统计数据
$stats = [];

try {
    // 用户统计
    $stmt = $pdo->query("SELECT COUNT(*) as total_users FROM users");
    $stats['total_users'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as new_users FROM users WHERE DATE(created_at) = CURDATE()");
    $stats['new_users'] = $stmt->fetchColumn();
    
    // 商品统计
    $stmt = $pdo->query("SELECT COUNT(*) as total_products FROM products");
    $stats['total_products'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as active_products FROM products WHERE status = 'active'");
    $stats['active_products'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as virtual_products FROM products WHERE is_virtual = 1");
    $stats['virtual_products'] = $stmt->fetchColumn();
    
    // 订单统计
    $stmt = $pdo->query("SELECT COUNT(*) as total_orders FROM orders");
    $stats['total_orders'] = $stmt->fetchColumn();
    
    $stmt = $pdo->query("SELECT COUNT(*) as today_orders FROM orders WHERE DATE(created_at) = CURDATE()");
    $stats['today_orders'] = $stmt->fetchColumn();
    
    // 收入统计
    $stmt = $pdo->query("SELECT COALESCE(SUM(total_price), 0) as total_revenue FROM orders WHERE status = 'completed'");
    $stats['total_revenue'] = $stmt->fetchColumn();
    
} catch (Exception $e) {
    // 处理错误
    $stats = [
        'total_users' => 0,
        'new_users' => 0,
        'total_products' => 0,
        'active_products' => 0,
        'virtual_products' => 0,
        'total_orders' => 0,
        'today_orders' => 0,
        'total_revenue' => 0
    ];
}

$pageTitle = '管理后台';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - 数字鱼管理系统</title>
    <link rel="stylesheet" href="../css/bootstrap-icons.css">
    <link rel="stylesheet" href="css/admin.css">
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>数字鱼管理</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="index.php">
                            <i class="bi bi-speedometer2"></i>
                            <span>仪表盘</span>
                        </a>
                    </li>
                    <li>
                        <a href="users.php">
                            <i class="bi bi-people"></i>
                            <span>用户管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="products.php">
                            <i class="bi bi-box-seam"></i>
                            <span>商品管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="categories.php">
                            <i class="bi bi-tags"></i>
                            <span>分类管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="orders.php">
                            <i class="bi bi-cart-check"></i>
                            <span>订单管理</span>
                        </a>
                    </li>
                    <li>
                        <a href="payment-settings.php">
                            <i class="bi bi-credit-card"></i>
                            <span>支付设置</span>
                        </a>
                    </li>
                    <li>
                        <a href="reports.php">
                            <i class="bi bi-graph-up"></i>
                            <span>数据报表</span>
                        </a>
                    </li>
                    <li>
                        <a href="settings.php">
                            <i class="bi bi-gear"></i>
                            <span>系统设置</span>
                        </a>
                    </li>
                    <li>
                        <a href="logout.php">
                            <i class="bi bi-box-arrow-right"></i>
                            <span>退出登录</span>
                        </a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- 主内容区 -->
        <main class="main-content">
            <!-- 顶部栏 -->
            <header class="top-bar">
                <div class="top-bar-left">
                    <h1>仪表盘</h1>
                </div>
                <div class="top-bar-right">
                    <span class="admin-info">管理员：<?php echo htmlspecialchars($_SESSION['admin_username'] ?? 'Admin'); ?></span>
                </div>
            </header>

            <!-- 统计卡片 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon users">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo number_format($stats['total_users']); ?></h3>
                        <p>总用户数</p>
                        <span class="stat-change">今日新增: <?php echo $stats['new_users']; ?></span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon products">
                        <i class="bi bi-box-seam"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo number_format($stats['total_products']); ?></h3>
                        <p>总商品数</p>
                        <span class="stat-change">在售: <?php echo $stats['active_products']; ?></span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon virtual">
                        <i class="bi bi-lightning-charge"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo number_format($stats['virtual_products']); ?></h3>
                        <p>虚拟商品</p>
                        <span class="stat-change">占比: <?php echo $stats['total_products'] > 0 ? round($stats['virtual_products'] / $stats['total_products'] * 100, 1) : 0; ?>%</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon orders">
                        <i class="bi bi-cart-check"></i>
                    </div>
                    <div class="stat-info">
                        <h3><?php echo number_format($stats['total_orders']); ?></h3>
                        <p>总订单数</p>
                        <span class="stat-change">今日: <?php echo $stats['today_orders']; ?></span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon revenue">
                        <i class="bi bi-currency-dollar"></i>
                    </div>
                    <div class="stat-info">
                        <h3>¥<?php echo number_format($stats['total_revenue'], 2); ?></h3>
                        <p>总收入</p>
                        <span class="stat-change">已完成订单</span>
                    </div>
                </div>
            </div>

            <!-- 快速操作 -->
            <div class="quick-actions">
                <h2>快速操作</h2>
                <div class="action-grid">
                    <a href="users.php?action=add" class="action-card">
                        <i class="bi bi-person-plus"></i>
                        <span>添加用户</span>
                    </a>
                    <a href="products.php?action=add" class="action-card">
                        <i class="bi bi-plus-square"></i>
                        <span>添加商品</span>
                    </a>
                    <a href="categories.php?action=add" class="action-card">
                        <i class="bi bi-tag"></i>
                        <span>添加分类</span>
                    </a>
                    <a href="reports.php" class="action-card">
                        <i class="bi bi-file-earmark-text"></i>
                        <span>查看报表</span>
                    </a>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="recent-activity">
                <h2>最近活动</h2>
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="bi bi-person-plus"></i>
                        </div>
                        <div class="activity-content">
                            <p>新用户注册</p>
                            <span class="activity-time">2分钟前</span>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="bi bi-box-seam"></i>
                        </div>
                        <div class="activity-content">
                            <p>新商品发布</p>
                            <span class="activity-time">5分钟前</span>
                        </div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon">
                            <i class="bi bi-cart-check"></i>
                        </div>
                        <div class="activity-content">
                            <p>订单完成</p>
                            <span class="activity-time">10分钟前</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
