<?php
session_start();
require_once '../config/database.php';

// 检查管理员登录状态
if (!isset($_SESSION['admin_id'])) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// 处理配置更新
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'update_payment_config') {
        try {
            $paymentMethod = $_POST['payment_method'] ?? '';
            $isEnabled = isset($_POST['is_enabled']) ? 1 : 0;
            $configData = [];
            
            // 根据不同支付方式处理配置数据
            switch ($paymentMethod) {
                case 'epay':
                    $configData = [
                        'api_url' => $_POST['epay_api_url'] ?? '',
                        'partner_id' => $_POST['epay_partner_id'] ?? '',
                        'key' => $_POST['epay_key'] ?? '',
                        'return_url' => $_POST['epay_return_url'] ?? '',
                        'notify_url' => $_POST['epay_notify_url'] ?? '',
                        'sandbox' => isset($_POST['epay_sandbox']) ? true : false
                    ];
                    break;
                case 'alipay':
                    $configData = [
                        'app_id' => $_POST['alipay_app_id'] ?? '',
                        'private_key' => $_POST['alipay_private_key'] ?? '',
                        'public_key' => $_POST['alipay_public_key'] ?? '',
                        'sandbox' => isset($_POST['alipay_sandbox']) ? true : false
                    ];
                    break;
                case 'wechat':
                    $configData = [
                        'app_id' => $_POST['wechat_app_id'] ?? '',
                        'mch_id' => $_POST['wechat_mch_id'] ?? '',
                        'key' => $_POST['wechat_key'] ?? '',
                        'sandbox' => isset($_POST['wechat_sandbox']) ? true : false
                    ];
                    break;
                case 'bank':
                    $configData = [
                        'merchant_id' => $_POST['bank_merchant_id'] ?? '',
                        'key' => $_POST['bank_key'] ?? '',
                        'sandbox' => isset($_POST['bank_sandbox']) ? true : false
                    ];
                    break;
            }
            
            // 更新配置
            $stmt = $pdo->prepare("
                UPDATE payment_config 
                SET is_enabled = ?, config_data = ?, updated_at = NOW()
                WHERE payment_method = ?
            ");
            $stmt->execute([$isEnabled, json_encode($configData), $paymentMethod]);
            
            $message = '支付配置更新成功';
        } catch (Exception $e) {
            $error = '配置更新失败：' . $e->getMessage();
        }
    }
}

// 获取所有支付配置
try {
    $stmt = $pdo->query("SELECT * FROM payment_config ORDER BY sort_order");
    $paymentConfigs = $stmt->fetchAll();
} catch (Exception $e) {
    $paymentConfigs = [];
    $error = '获取支付配置失败：' . $e->getMessage();
}

$pageTitle = '支付设置';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - 数字鱼管理系统</title>
    <link rel="stylesheet" href="../css/bootstrap-icons.css">
    <link rel="stylesheet" href="css/admin.css">
    <style>
        .payment-config-card {
            background: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .payment-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }
        .payment-title {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }
        .payment-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }
        .status-enabled {
            background: #d4edda;
            color: #155724;
        }
        .status-disabled {
            background: #f8d7da;
            color: #721c24;
        }
        .config-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
        }
        .form-group {
            margin-bottom: 1rem;
        }
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: #555;
        }
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .btn-save {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .btn-save:hover {
            background: #218838;
        }
        .config-section {
            margin-top: 1rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
        }
    </style>
</head>
<body>
    <div class="admin-container">
        <!-- 侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h2>数字鱼管理</h2>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li><a href="index.php"><i class="bi bi-speedometer2"></i><span>仪表盘</span></a></li>
                    <li><a href="users.php"><i class="bi bi-people"></i><span>用户管理</span></a></li>
                    <li><a href="products.php"><i class="bi bi-box-seam"></i><span>商品管理</span></a></li>
                    <li><a href="orders.php"><i class="bi bi-receipt"></i><span>订单管理</span></a></li>
                    <li><a href="categories.php"><i class="bi bi-tags"></i><span>分类管理</span></a></li>
                    <li class="active"><a href="payment-settings.php"><i class="bi bi-credit-card"></i><span>支付设置</span></a></li>
                    <li><a href="sms-management.php"><i class="bi bi-chat-dots"></i><span>短信管理</span></a></li>
                    <li><a href="settings.php"><i class="bi bi-gear"></i><span>系统设置</span></a></li>
                    <li><a href="logout.php"><i class="bi bi-box-arrow-right"></i><span>退出登录</span></a></li>
                </ul>
            </nav>
        </aside>

        <!-- 主要内容区 -->
        <main class="main-content">
            <div class="content-header">
                <h1><?php echo $pageTitle; ?></h1>
                <p>管理网站支付方式配置</p>
            </div>

            <?php if ($message): ?>
                <div class="alert alert-success">
                    <i class="bi bi-check-circle"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <?php if ($error): ?>
                <div class="alert alert-error">
                    <i class="bi bi-exclamation-triangle"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <div class="content-body">
                <?php foreach ($paymentConfigs as $config): ?>
                    <?php 
                    $configData = json_decode($config['config_data'], true) ?: [];
                    $methodIcons = [
                        'alipay' => 'bi-phone',
                        'wechat' => 'bi-wechat', 
                        'bank' => 'bi-credit-card',
                        'epay' => 'bi-lightning-charge'
                    ];
                    ?>
                    <div class="payment-config-card">
                        <div class="payment-header">
                            <div class="payment-title">
                                <i class="bi <?php echo $methodIcons[$config['payment_method']] ?? 'bi-credit-card'; ?>"></i>
                                <?php echo htmlspecialchars($config['display_name']); ?>
                            </div>
                            <div class="payment-status">
                                <span class="status-badge <?php echo $config['is_enabled'] ? 'status-enabled' : 'status-disabled'; ?>">
                                    <?php echo $config['is_enabled'] ? '已启用' : '已禁用'; ?>
                                </span>
                            </div>
                        </div>

                        <form method="POST" class="config-form">
                            <input type="hidden" name="action" value="update_payment_config">
                            <input type="hidden" name="payment_method" value="<?php echo $config['payment_method']; ?>">
                            
                            <div class="form-group">
                                <div class="checkbox-group">
                                    <input type="checkbox" name="is_enabled" id="enabled_<?php echo $config['payment_method']; ?>" 
                                           <?php echo $config['is_enabled'] ? 'checked' : ''; ?>>
                                    <label for="enabled_<?php echo $config['payment_method']; ?>">启用此支付方式</label>
                                </div>
                            </div>

                            <?php if ($config['payment_method'] == 'epay'): ?>
                                <div class="config-section">
                                    <div class="form-group">
                                        <label>API接口地址</label>
                                        <input type="url" name="epay_api_url" 
                                               value="<?php echo htmlspecialchars($configData['api_url'] ?? ''); ?>"
                                               placeholder="https://pay.example.com/api/">
                                    </div>
                                    <div class="form-group">
                                        <label>商户ID</label>
                                        <input type="text" name="epay_partner_id" 
                                               value="<?php echo htmlspecialchars($configData['partner_id'] ?? ''); ?>"
                                               placeholder="请输入商户ID">
                                    </div>
                                    <div class="form-group">
                                        <label>商户密钥</label>
                                        <input type="password" name="epay_key" 
                                               value="<?php echo htmlspecialchars($configData['key'] ?? ''); ?>"
                                               placeholder="请输入商户密钥">
                                    </div>
                                    <div class="form-group">
                                        <label>同步回调地址</label>
                                        <input type="url" name="epay_return_url" 
                                               value="<?php echo htmlspecialchars($configData['return_url'] ?? ''); ?>"
                                               placeholder="<?php echo SITE_URL; ?>/api/epay-return.php">
                                    </div>
                                    <div class="form-group">
                                        <label>异步通知地址</label>
                                        <input type="url" name="epay_notify_url" 
                                               value="<?php echo htmlspecialchars($configData['notify_url'] ?? ''); ?>"
                                               placeholder="<?php echo SITE_URL; ?>/api/epay-notify.php">
                                    </div>
                                    <div class="form-group">
                                        <div class="checkbox-group">
                                            <input type="checkbox" name="epay_sandbox" id="epay_sandbox" 
                                                   <?php echo ($configData['sandbox'] ?? false) ? 'checked' : ''; ?>>
                                            <label for="epay_sandbox">沙箱模式（测试环境）</label>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="form-group">
                                <button type="submit" class="btn-save">
                                    <i class="bi bi-check"></i> 保存配置
                                </button>
                            </div>
                        </form>
                    </div>
                <?php endforeach; ?>
            </div>
        </main>
    </div>
</body>
</html>
