<?php
/**
 * 登录注册API测试页面
 */
$pageTitle = '登录注册测试';
require_once 'includes/header.php';
?>

<main class="main">
    <div class="container">
        <div class="page-header">
            <h1>登录注册API测试</h1>
            <p>测试登录和注册功能是否正常工作</p>
        </div>

        <div class="test-section">
            <h2>登录测试</h2>
            <form id="testLoginForm" style="max-width: 400px; margin-bottom: 30px;">
                <div style="margin-bottom: 15px;">
                    <label>账号:</label>
                    <input type="text" name="account" value="<EMAIL>" style="width: 100%; padding: 8px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label>密码:</label>
                    <input type="password" name="password" value="123456" style="width: 100%; padding: 8px;">
                </div>
                <button type="submit" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px;">测试登录</button>
            </form>
            <div id="loginResult" style="margin-top: 15px; padding: 10px; border-radius: 4px; display: none;"></div>
        </div>

        <div class="test-section">
            <h2>注册测试</h2>
            <form id="testRegisterForm" style="max-width: 400px; margin-bottom: 30px;">
                <div style="margin-bottom: 15px;">
                    <label>用户名:</label>
                    <input type="text" name="username" value="testuser" style="width: 100%; padding: 8px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label>邮箱:</label>
                    <input type="email" name="email" value="<EMAIL>" style="width: 100%; padding: 8px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label>手机号:</label>
                    <input type="tel" name="phone" value="13800138000" style="width: 100%; padding: 8px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label>密码:</label>
                    <input type="password" name="password" value="123456" style="width: 100%; padding: 8px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label>确认密码:</label>
                    <input type="password" name="confirm_password" value="123456" style="width: 100%; padding: 8px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label>短信验证码:</label>
                    <input type="text" name="sms_code" value="123456" style="width: 100%; padding: 8px;">
                </div>
                <div style="margin-bottom: 15px;">
                    <label>
                        <input type="checkbox" name="agree_terms" checked> 同意用户协议
                    </label>
                </div>
                <button type="submit" style="padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px;">测试注册</button>
            </form>
            <div id="registerResult" style="margin-top: 15px; padding: 10px; border-radius: 4px; display: none;"></div>
        </div>

        <div class="test-section">
            <h2>数据库连接测试</h2>
            <button id="testDbBtn" style="padding: 10px 20px; background: #17a2b8; color: white; border: none; border-radius: 4px;">测试数据库连接</button>
            <div id="dbResult" style="margin-top: 15px; padding: 10px; border-radius: 4px; display: none;"></div>
        </div>
    </div>
</main>

<script>
// 登录测试
document.getElementById('testLoginForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    formData.append('login_type', 'account');
    
    const resultDiv = document.getElementById('loginResult');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = '测试中...';
    resultDiv.style.background = '#f8f9fa';
    resultDiv.style.color = '#495057';
    
    fetch('api/login.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = '✅ 登录API工作正常<br>' + JSON.stringify(data, null, 2);
            resultDiv.style.background = '#d4edda';
            resultDiv.style.color = '#155724';
        } else {
            resultDiv.innerHTML = '❌ 登录失败: ' + data.message + '<br>' + JSON.stringify(data, null, 2);
            resultDiv.style.background = '#f8d7da';
            resultDiv.style.color = '#721c24';
        }
    })
    .catch(error => {
        resultDiv.innerHTML = '❌ 网络错误: ' + error.message;
        resultDiv.style.background = '#f8d7da';
        resultDiv.style.color = '#721c24';
    });
});

// 注册测试
document.getElementById('testRegisterForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    const resultDiv = document.getElementById('registerResult');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = '测试中...';
    resultDiv.style.background = '#f8f9fa';
    resultDiv.style.color = '#495057';
    
    fetch('api/register.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = '✅ 注册API工作正常<br>' + JSON.stringify(data, null, 2);
            resultDiv.style.background = '#d4edda';
            resultDiv.style.color = '#155724';
        } else {
            resultDiv.innerHTML = '❌ 注册失败: ' + data.message + '<br>' + JSON.stringify(data, null, 2);
            resultDiv.style.background = '#f8d7da';
            resultDiv.style.color = '#721c24';
        }
    })
    .catch(error => {
        resultDiv.innerHTML = '❌ 网络错误: ' + error.message;
        resultDiv.style.background = '#f8d7da';
        resultDiv.style.color = '#721c24';
    });
});

// 数据库连接测试
document.getElementById('testDbBtn').addEventListener('click', function() {
    const resultDiv = document.getElementById('dbResult');
    resultDiv.style.display = 'block';
    resultDiv.innerHTML = '测试中...';
    resultDiv.style.background = '#f8f9fa';
    resultDiv.style.color = '#495057';
    
    fetch('api/test-db.php')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = '✅ 数据库连接正常<br>' + JSON.stringify(data, null, 2);
            resultDiv.style.background = '#d4edda';
            resultDiv.style.color = '#155724';
        } else {
            resultDiv.innerHTML = '❌ 数据库连接失败: ' + data.message + '<br>' + JSON.stringify(data, null, 2);
            resultDiv.style.background = '#f8d7da';
            resultDiv.style.color = '#721c24';
        }
    })
    .catch(error => {
        resultDiv.innerHTML = '❌ 网络错误: ' + error.message;
        resultDiv.style.background = '#f8d7da';
        resultDiv.style.color = '#721c24';
    });
});
</script>

<style>
.test-section {
    background: white;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-section h2 {
    margin-top: 0;
    color: #333;
}

.test-section label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.test-section input[type="text"],
.test-section input[type="email"],
.test-section input[type="tel"],
.test-section input[type="password"] {
    border: 1px solid #ddd;
    border-radius: 4px;
}

.test-section input[type="text"]:focus,
.test-section input[type="email"]:focus,
.test-section input[type="tel"]:focus,
.test-section input[type="password"]:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.test-section button:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

pre {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
    font-size: 12px;
}
</style>

<?php require_once 'includes/footer.php'; ?>
