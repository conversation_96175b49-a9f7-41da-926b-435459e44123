<?php
/**
 * 易支付功能测试页面
 */
require_once 'includes/functions.php';

// 检查登录状态
if (!isLoggedIn()) {
    $_SESSION['error_message'] = '请先登录';
    redirect('login.php');
}

$message = '';
$error = '';

// 处理测试请求
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'test_config') {
        try {
            require_once 'includes/epay.php';
            $epayService = new EPayService($pdo);
            
            if ($epayService->isConfigValid()) {
                $message = '易支付配置有效';
            } else {
                $error = '易支付配置无效，请检查配置参数';
            }
        } catch (Exception $e) {
            $error = '测试失败：' . $e->getMessage();
        }
    }
    
    if ($action == 'create_test_payment') {
        try {
            require_once 'includes/epay.php';
            $epayService = new EPayService($pdo);
            
            $testAmount = floatval($_POST['amount'] ?? 0.01);
            $testOrderId = 999999; // 测试订单ID
            
            $paymentResult = $epayService->createPayment($testOrderId, $testAmount, '测试支付');
            
            $message = '测试支付链接创建成功';
            $testPayUrl = $paymentResult['pay_url'];
            
        } catch (Exception $e) {
            $error = '创建测试支付失败：' . $e->getMessage();
        }
    }
}

// 获取易支付配置
try {
    $stmt = $pdo->prepare("SELECT * FROM payment_config WHERE payment_method = 'epay'");
    $stmt->execute();
    $epayConfig = $stmt->fetch();
    
    if ($epayConfig) {
        $configData = json_decode($epayConfig['config_data'], true) ?: [];
    }
} catch (Exception $e) {
    $error = '获取配置失败：' . $e->getMessage();
}

$pageTitle = '易支付测试';
$additionalCSS = ['css/member.css'];
require_once 'includes/header.php';
?>

<main class="main member-page">
    <div class="container">
        <div class="member-container">
            <div class="member-content">
                <div class="page-header">
                    <h1 class="page-title">
                        <i class="bi bi-lightning-charge"></i>
                        易支付功能测试
                    </h1>
                    <p>测试易支付配置和功能是否正常</p>
                </div>

                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                <?php endif; ?>

                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <i class="bi bi-exclamation-triangle"></i>
                        <?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <!-- 配置信息 -->
                <div class="info-card">
                    <h3>当前配置</h3>
                    <?php if (isset($epayConfig)): ?>
                        <div class="config-info">
                            <div class="config-item">
                                <label>状态：</label>
                                <span class="status <?php echo $epayConfig['is_enabled'] ? 'enabled' : 'disabled'; ?>">
                                    <?php echo $epayConfig['is_enabled'] ? '已启用' : '已禁用'; ?>
                                </span>
                            </div>
                            <div class="config-item">
                                <label>API地址：</label>
                                <span><?php echo htmlspecialchars($configData['api_url'] ?? '未配置'); ?></span>
                            </div>
                            <div class="config-item">
                                <label>商户ID：</label>
                                <span><?php echo htmlspecialchars($configData['partner_id'] ?? '未配置'); ?></span>
                            </div>
                            <div class="config-item">
                                <label>回调地址：</label>
                                <span><?php echo htmlspecialchars($configData['notify_url'] ?? '未配置'); ?></span>
                            </div>
                            <div class="config-item">
                                <label>沙箱模式：</label>
                                <span><?php echo ($configData['sandbox'] ?? false) ? '是' : '否'; ?></span>
                            </div>
                        </div>
                    <?php else: ?>
                        <p class="no-config">易支付配置不存在，请先运行数据库初始化脚本</p>
                    <?php endif; ?>
                </div>

                <!-- 测试功能 -->
                <div class="test-actions">
                    <h3>测试功能</h3>
                    
                    <div class="test-section">
                        <h4>配置测试</h4>
                        <p>测试易支付配置是否有效</p>
                        <form method="POST">
                            <input type="hidden" name="action" value="test_config">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle"></i> 测试配置
                            </button>
                        </form>
                    </div>

                    <div class="test-section">
                        <h4>支付测试</h4>
                        <p>创建测试支付链接（不会实际扣费）</p>
                        <form method="POST">
                            <input type="hidden" name="action" value="create_test_payment">
                            <div class="form-group">
                                <label>测试金额：</label>
                                <input type="number" name="amount" value="0.01" min="0.01" max="1000" step="0.01" required>
                                <small>建议使用小额测试</small>
                            </div>
                            <button type="submit" class="btn btn-warning">
                                <i class="bi bi-credit-card"></i> 创建测试支付
                            </button>
                        </form>
                        
                        <?php if (isset($testPayUrl)): ?>
                            <div class="test-result">
                                <h5>测试支付链接：</h5>
                                <div class="pay-url">
                                    <input type="text" value="<?php echo htmlspecialchars($testPayUrl); ?>" readonly>
                                    <button onclick="window.open('<?php echo htmlspecialchars($testPayUrl); ?>')" class="btn btn-outline">
                                        打开支付页面
                                    </button>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- 帮助信息 -->
                <div class="help-info">
                    <h3>使用说明</h3>
                    <ul>
                        <li>请先在后台管理中配置易支付参数</li>
                        <li>确保API地址、商户ID和密钥正确</li>
                        <li>回调地址需要能被易支付平台访问</li>
                        <li>测试时建议先使用沙箱模式</li>
                        <li>生产环境请关闭沙箱模式</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</main>

<style>
.config-info {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-top: 10px;
}

.config-item {
    display: flex;
    margin-bottom: 8px;
}

.config-item label {
    width: 100px;
    font-weight: 500;
}

.status.enabled {
    color: #28a745;
    font-weight: 500;
}

.status.disabled {
    color: #dc3545;
    font-weight: 500;
}

.test-section {
    background: white;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 15px;
    border: 1px solid #e9ecef;
}

.test-section h4 {
    margin-bottom: 8px;
    color: #495057;
}

.test-result {
    margin-top: 15px;
    padding: 15px;
    background: #e7f3ff;
    border-radius: 8px;
}

.pay-url {
    display: flex;
    gap: 10px;
    margin-top: 8px;
}

.pay-url input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.help-info ul {
    padding-left: 20px;
}

.help-info li {
    margin-bottom: 5px;
}

.no-config {
    color: #dc3545;
    font-style: italic;
}
</style>

<?php require_once 'includes/footer.php'; ?>
