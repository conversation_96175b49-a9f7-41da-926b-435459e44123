# 易支付功能设置指南

## 📋 功能概述

易支付是一个第三方支付平台，支持支付宝、微信支付、QQ钱包等多种支付方式。本指南将帮助您在数字鱼平台中集成易支付功能。

## 🚀 快速安装

### 1. 运行安装脚本

访问安装页面：
```
http://your-domain.com/xianyu/install-epay.php
```

按照页面提示完成以下步骤：
- 创建数据库表
- 检查文件权限
- 配置易支付参数
- 测试功能

### 2. 手动安装（可选）

如果自动安装失败，可以手动执行：

```bash
# 创建数据库表
php create-payment-tables.php

# 检查权限
chmod 755 logs/
chmod 755 api/
```

## ⚙️ 配置说明

### 后台配置

1. 登录后台管理系统
2. 进入 "支付设置" 页面
3. 找到 "易支付" 配置项
4. 填写以下参数：

| 参数名称 | 说明 | 示例 |
|---------|------|------|
| API接口地址 | 易支付平台提供的API地址 | `https://pay.example.com/api/` |
| 商户ID | 您在易支付平台的商户ID | `10001` |
| 商户密钥 | 易支付平台提供的密钥 | `your_secret_key` |
| 同步回调地址 | 支付成功后跳转地址（自动填充） | `http://your-domain.com/api/epay-return.php` |
| 异步通知地址 | 支付结果通知地址（自动填充） | `http://your-domain.com/api/epay-notify.php` |
| 沙箱模式 | 测试环境开关 | 开发时建议开启 |

### 回调地址配置

确保以下地址能被易支付平台访问：

**异步通知地址（必须）：**
```
http://your-domain.com/xianyu/api/epay-notify.php
```

**同步回调地址（可选）：**
```
http://your-domain.com/xianyu/api/epay-return.php
```

> **注意：** 如果是本地开发环境，需要使用内网穿透工具（如 ngrok）使回调地址能被外网访问。

## 🧪 测试功能

### 使用测试页面

访问测试页面：
```
http://your-domain.com/xianyu/test-epay.php
```

测试功能包括：
- 配置有效性检查
- 创建测试支付链接
- 查看配置信息

### 手动测试流程

1. 确保易支付配置正确
2. 创建一个测试商品
3. 使用测试账号购买商品
4. 选择易支付方式
5. 完成支付流程
6. 检查订单状态更新

## 📁 文件结构

```
xianyu/
├── includes/
│   └── epay.php                 # 易支付处理类
├── api/
│   ├── epay-notify.php          # 异步通知处理
│   └── epay-return.php          # 同步回调处理
├── admin/
│   └── payment-settings.php     # 后台支付设置
├── css/
│   └── pay.css                  # 支付页面样式（已更新）
├── logs/
│   ├── epay_notify.log          # 异步通知日志
│   └── epay_return.log          # 同步回调日志
├── pay.php                      # 支付页面（已更新）
├── test-epay.php               # 测试页面
├── install-epay.php            # 安装页面
├── create-payment-tables.php   # 数据库初始化
└── EPAY_SETUP_GUIDE.md         # 本文档
```

## 🔧 数据库表结构

### payment_config 表

存储支付方式配置信息：

```sql
CREATE TABLE payment_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    payment_method VARCHAR(50) NOT NULL UNIQUE,
    display_name VARCHAR(100) NOT NULL,
    is_enabled BOOLEAN DEFAULT FALSE,
    config_data JSON,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### orders 表新增字段

```sql
ALTER TABLE orders ADD COLUMN payment_method VARCHAR(50) DEFAULT NULL;
ALTER TABLE orders ADD COLUMN payment_time TIMESTAMP NULL;
ALTER TABLE orders ADD COLUMN trade_no VARCHAR(100) DEFAULT NULL;
```

## 🐛 故障排除

### 常见问题

**1. 支付后订单状态未更新**
- 检查异步通知地址是否正确
- 查看 `logs/epay_notify.log` 日志
- 确认易支付平台能访问通知地址

**2. 支付页面无法跳转**
- 检查易支付配置是否正确
- 确认API地址格式正确
- 查看浏览器控制台错误信息

**3. 签名验证失败**
- 检查商户密钥是否正确
- 确认参数编码格式
- 查看异步通知日志

### 日志查看

**异步通知日志：**
```bash
tail -f logs/epay_notify.log
```

**同步回调日志：**
```bash
tail -f logs/epay_return.log
```

## 🔒 安全建议

1. **生产环境配置**
   - 关闭沙箱模式
   - 使用HTTPS协议
   - 定期更换商户密钥

2. **服务器配置**
   - 确保logs目录权限正确
   - 定期清理日志文件
   - 监控异常访问

3. **代码安全**
   - 不要在前端暴露密钥
   - 验证所有回调参数
   - 记录所有支付操作

## 📞 技术支持

如果遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查日志文件获取详细错误信息
3. 确认易支付平台配置正确
4. 联系易支付平台技术支持

## 📝 更新日志

- **v1.0.0** - 初始版本，支持基本支付功能
- 支持支付宝、微信支付等多种方式
- 完整的回调处理机制
- 后台配置管理界面
- 详细的日志记录功能
