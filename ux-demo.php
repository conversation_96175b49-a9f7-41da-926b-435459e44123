<?php
/**
 * UX优化功能演示页面
 */
require_once 'includes/functions.php';

$pageTitle = 'UX优化演示';
$additionalCSS = ['css/member.css'];
require_once 'includes/header.php';
?>

<main class="main">
    <div class="container">
        <div class="page-header">
            <h1 class="page-title">
                <i class="bi bi-magic"></i>
                用户体验优化演示
            </h1>
            <p>体验数字鱼平台的现代化交互功能</p>
        </div>

        <!-- 通知系统演示 -->
        <div class="demo-section">
            <h2>📢 消息通知系统</h2>
            <p>现代化的消息提示，支持多种类型和自定义操作</p>
            <div class="demo-buttons">
                <button class="btn btn-success" onclick="demoNotifications('success')">成功消息</button>
                <button class="btn btn-danger" onclick="demoNotifications('error')">错误消息</button>
                <button class="btn btn-warning" onclick="demoNotifications('warning')">警告消息</button>
                <button class="btn btn-info" onclick="demoNotifications('info')">信息提示</button>
                <button class="btn btn-primary" onclick="demoNotifications('action')">带操作按钮</button>
                <button class="btn btn-secondary" onclick="demoNotifications('loading')">加载状态</button>
            </div>
        </div>

        <!-- 确认对话框演示 -->
        <div class="demo-section">
            <h2>💬 确认对话框</h2>
            <p>美观的确认对话框，支持多种交互方式</p>
            <div class="demo-buttons">
                <button class="btn btn-primary" onclick="demoConfirm('basic')">基础确认</button>
                <button class="btn btn-warning" onclick="demoConfirm('warning')">警告确认</button>
                <button class="btn btn-danger" onclick="demoConfirm('danger')">危险操作</button>
                <button class="btn btn-info" onclick="demoConfirm('prompt')">输入对话框</button>
                <button class="btn btn-success" onclick="demoConfirm('details')">详细信息</button>
            </div>
        </div>

        <!-- 加载状态演示 -->
        <div class="demo-section">
            <h2>⏳ 加载状态管理</h2>
            <p>统一的加载指示和进度反馈</p>
            <div class="demo-buttons">
                <button class="btn btn-primary" onclick="demoLoading('overlay')">全屏加载</button>
                <button class="btn btn-secondary" onclick="demoLoading('button')" id="demo-btn">按钮加载</button>
                <button class="btn btn-info" onclick="demoLoading('progress')">进度条</button>
                <button class="btn btn-success" onclick="demoLoading('form')">表单提交</button>
            </div>
            <div id="progress-demo" style="margin-top: 20px;"></div>
        </div>

        <!-- 用户引导演示 -->
        <div class="demo-section">
            <h2>🎯 用户引导系统</h2>
            <p>新手引导和操作提示</p>
            <div class="demo-buttons">
                <button class="btn btn-primary" onclick="demoGuide('tour')" id="guide-start">开始引导</button>
                <button class="btn btn-info" onclick="demoGuide('hint')" id="guide-hint">显示提示</button>
                <button class="btn btn-success" onclick="demoGuide('welcome')">欢迎引导</button>
            </div>
        </div>

        <!-- 表单增强演示 -->
        <div class="demo-section">
            <h2>📝 表单增强功能</h2>
            <p>实时验证和用户友好的错误提示</p>
            <form class="demo-form" onsubmit="return demoFormSubmit(event)">
                <div class="form-group">
                    <label>邮箱地址</label>
                    <input type="email" name="email" required placeholder="请输入邮箱地址">
                </div>
                <div class="form-group">
                    <label>手机号码</label>
                    <input type="tel" name="phone" pattern="[0-9]{11}" required placeholder="请输入11位手机号">
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" name="password" minlength="6" required placeholder="至少6位字符">
                </div>
                <div class="form-group">
                    <label>个人简介</label>
                    <textarea name="bio" maxlength="200" placeholder="简单介绍一下自己"></textarea>
                </div>
                <button type="submit" class="btn btn-primary">提交表单</button>
            </form>
        </div>

        <!-- 交互效果演示 -->
        <div class="demo-section">
            <h2>✨ 交互效果</h2>
            <p>现代化的视觉反馈和动画效果</p>
            <div class="demo-cards">
                <div class="demo-card">
                    <h3>悬停效果</h3>
                    <p>鼠标悬停时的卡片动画</p>
                </div>
                <div class="demo-card">
                    <h3>点击反馈</h3>
                    <p>点击时的波纹效果</p>
                </div>
                <div class="demo-card">
                    <h3>焦点状态</h3>
                    <p>键盘导航的焦点指示</p>
                </div>
            </div>
        </div>

        <!-- 无障碍功能演示 -->
        <div class="demo-section">
            <h2>♿ 无障碍功能</h2>
            <p>支持键盘导航和屏幕阅读器</p>
            <div class="accessibility-demo">
                <p><strong>键盘快捷键：</strong></p>
                <ul>
                    <li><kbd>Ctrl/Cmd + K</kbd> - 打开搜索</li>
                    <li><kbd>ESC</kbd> - 关闭模态框</li>
                    <li><kbd>Tab</kbd> - 切换焦点</li>
                    <li><kbd>Enter</kbd> - 激活按钮</li>
                </ul>
            </div>
        </div>
    </div>
</main>

<style>
.demo-section {
    background: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-section h2 {
    margin-bottom: 8px;
    color: #212529;
}

.demo-section p {
    color: #6c757d;
    margin-bottom: 20px;
}

.demo-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    margin-bottom: 16px;
}

.demo-form {
    max-width: 400px;
}

.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    margin-bottom: 4px;
    font-weight: 500;
    color: #495057;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    font-size: 14px;
}

.demo-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
}

.demo-card {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
}

.demo-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.demo-card h3 {
    margin-bottom: 8px;
    color: #212529;
}

.demo-card p {
    color: #6c757d;
    margin: 0;
}

.accessibility-demo {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 16px;
}

.accessibility-demo ul {
    margin: 8px 0 0 0;
    padding-left: 20px;
}

.accessibility-demo li {
    margin-bottom: 4px;
}

kbd {
    background: #212529;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
}

@media (max-width: 768px) {
    .demo-buttons {
        flex-direction: column;
    }
    
    .demo-cards {
        grid-template-columns: 1fr;
    }
}
</style>

<script>
// 通知系统演示
function demoNotifications(type) {
    switch (type) {
        case 'success':
            notify.success('操作成功', '您的操作已成功完成！');
            break;
        case 'error':
            notify.error('操作失败', '抱歉，操作过程中出现了错误');
            break;
        case 'warning':
            notify.warning('注意', '请注意检查您的输入信息');
            break;
        case 'info':
            notify.info('提示信息', '这是一条普通的信息提示');
            break;
        case 'action':
            notify.show({
                type: 'info',
                title: '确认操作',
                message: '是否要继续执行此操作？',
                actions: [
                    {
                        text: '取消',
                        handler: () => notify.info('已取消', '操作已取消')
                    },
                    {
                        text: '确认',
                        primary: true,
                        handler: () => notify.success('已确认', '操作已确认执行')
                    }
                ]
            });
            break;
        case 'loading':
            const loadingId = notify.loading('处理中', '正在处理您的请求...');
            setTimeout(() => {
                notify.close(loadingId);
                notify.success('完成', '处理完成！');
            }, 3000);
            break;
    }
}

// 确认对话框演示
async function demoConfirm(type) {
    let result;
    
    switch (type) {
        case 'basic':
            result = await confirm.confirm('您确定要执行此操作吗？');
            break;
        case 'warning':
            result = await confirm.warning('此操作可能会影响您的数据，确定继续吗？');
            break;
        case 'danger':
            result = await confirm.danger('此操作不可撤销，确定要删除吗？', '危险操作');
            break;
        case 'prompt':
            result = await confirm.prompt('请输入您的姓名', '个人信息', '请输入姓名');
            break;
        case 'details':
            result = await confirm.show({
                type: 'info',
                title: '订单确认',
                message: '请确认您的订单信息',
                details: [
                    { label: '商品名称', value: '虚拟商品示例' },
                    { label: '价格', value: '¥99.00' },
                    { label: '数量', value: '1' }
                ]
            });
            break;
    }
    
    if (result.confirmed) {
        const message = result.inputValue ? `您输入了：${result.inputValue}` : '您确认了操作';
        notify.success('确认成功', message);
    } else {
        notify.info('已取消', '您取消了操作');
    }
}

// 加载状态演示
function demoLoading(type) {
    switch (type) {
        case 'overlay':
            const overlay = loading.showOverlay('加载中', '正在获取数据...');
            setTimeout(() => {
                loading.hideOverlay(overlay);
                notify.success('加载完成', '数据获取成功！');
            }, 3000);
            break;
        case 'button':
            const btn = document.getElementById('demo-btn');
            loading.setButtonLoading(btn, true);
            setTimeout(() => {
                loading.setButtonLoading(btn, false);
                notify.success('完成', '按钮操作完成！');
            }, 2000);
            break;
        case 'progress':
            const container = document.getElementById('progress-demo');
            const progressBar = loading.createProgressBar(0);
            container.innerHTML = '<h4>下载进度</h4>';
            container.appendChild(progressBar);
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                loading.updateProgress(progressBar, progress);
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        container.innerHTML = '';
                        notify.success('下载完成', '文件下载成功！');
                    }, 500);
                }
            }, 200);
            break;
        case 'form':
            const form = document.querySelector('.demo-form');
            const handler = loading.handleFormSubmit(form);
            setTimeout(() => {
                handler.success('表单提交成功！');
            }, 2000);
            break;
    }
}

// 用户引导演示
function demoGuide(type) {
    switch (type) {
        case 'tour':
            const steps = [
                {
                    target: '#guide-start',
                    title: '开始引导',
                    content: '点击这个按钮可以开始用户引导流程',
                    position: 'bottom'
                },
                {
                    target: '#guide-hint',
                    title: '提示功能',
                    content: '这个按钮可以显示快速提示信息',
                    position: 'bottom'
                },
                {
                    target: '.demo-form',
                    title: '表单功能',
                    content: '这里展示了增强的表单验证功能',
                    position: 'top'
                }
            ];
            
            guide.startTour(steps, {
                onComplete: () => notify.success('引导完成', '您已完成演示引导！'),
                onSkip: () => notify.info('已跳过', '您跳过了引导流程')
            });
            break;
        case 'hint':
            guide.showHint('#guide-hint', '这是一个快速提示！');
            break;
        case 'welcome':
            guide.showWelcomeTour();
            break;
    }
}

// 表单提交演示
function demoFormSubmit(event) {
    event.preventDefault();
    
    const handler = loading.handleFormSubmit(event.target);
    
    // 模拟异步提交
    setTimeout(() => {
        handler.success('表单提交成功！数据已保存');
    }, 2000);
    
    return false;
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 显示欢迎消息
    setTimeout(() => {
        notify.info('欢迎体验', '这里展示了数字鱼平台的UX优化功能');
    }, 1000);
});
</script>

<?php require_once 'includes/footer.php'; ?>
