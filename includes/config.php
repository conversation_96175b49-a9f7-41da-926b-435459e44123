<?php
/**
 * 数据库和系统配置文件
 * 供API文件使用
 */

// 数据库配置
$host = 'localhost';
$dbname = 'xianyu_db';
$username_db = 'xianyu_db';
$password_db = '2CY9SsWpXs6yWHks';
$charset = 'utf8mb4';

// 构建DSN
$dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";

// 网站配置
define('SITE_NAME', '数字鱼');
define('SITE_URL', 'http://localhost/xianyu');
define('UPLOAD_PATH', '../uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// 短信配置
define('SMS_PROVIDER', 'mock'); // 使用模拟短信服务
define('SMS_ACCESS_KEY', 'your_access_key');
define('SMS_ACCESS_SECRET', 'your_access_secret');
define('SMS_SIGN_NAME', '数字鱼');
define('SMS_TEMPLATE_CODE', 'SMS_123456789');
define('SMS_RATE_LIMIT', 60); // 发送间隔限制（秒）
define('SMS_DAILY_LIMIT', 10); // 每日发送限制

// 会话配置
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// 错误报告（开发环境）
// 注意：API文件会单独设置错误报告级别
if (!defined('API_MODE')) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}
?>
