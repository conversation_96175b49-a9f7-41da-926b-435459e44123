<?php
/**
 * 易支付接口处理类
 */
class EPayService {
    private $config;
    private $pdo;
    
    public function __construct($pdo) {
        $this->pdo = $pdo;
        $this->loadConfig();
    }
    
    /**
     * 加载易支付配置
     */
    private function loadConfig() {
        try {
            $stmt = $this->pdo->prepare("SELECT config_data FROM payment_config WHERE payment_method = 'epay' AND is_enabled = 1");
            $stmt->execute();
            $result = $stmt->fetch();
            
            if ($result) {
                $this->config = json_decode($result['config_data'], true);
            } else {
                throw new Exception('易支付未启用或配置不存在');
            }
        } catch (Exception $e) {
            throw new Exception('加载易支付配置失败：' . $e->getMessage());
        }
    }
    
    /**
     * 创建支付订单
     */
    public function createPayment($orderId, $amount, $subject = '商品购买') {
        if (!$this->config) {
            throw new Exception('易支付配置未加载');
        }
        
        // 构建支付参数
        $params = [
            'pid' => $this->config['partner_id'],
            'type' => 'alipay', // 默认使用支付宝
            'out_trade_no' => 'XY' . $orderId . '_' . time(),
            'notify_url' => $this->config['notify_url'],
            'return_url' => $this->config['return_url'],
            'name' => $subject,
            'money' => number_format($amount, 2, '.', ''),
            'sitename' => SITE_NAME ?? '数字鱼'
        ];
        
        // 生成签名
        $params['sign'] = $this->generateSign($params);
        $params['sign_type'] = 'MD5';
        
        // 构建支付URL
        $payUrl = $this->config['api_url'] . 'submit.php?' . http_build_query($params);
        
        return [
            'pay_url' => $payUrl,
            'trade_no' => $params['out_trade_no'],
            'params' => $params
        ];
    }
    
    /**
     * 生成签名
     */
    private function generateSign($params) {
        // 移除空值和签名参数
        $signParams = array_filter($params, function($value, $key) {
            return $value !== '' && $key !== 'sign' && $key !== 'sign_type';
        }, ARRAY_FILTER_USE_BOTH);
        
        // 按键名排序
        ksort($signParams);
        
        // 构建签名字符串
        $signString = '';
        foreach ($signParams as $key => $value) {
            $signString .= $key . '=' . $value . '&';
        }
        $signString .= 'key=' . $this->config['key'];
        
        return md5($signString);
    }
    
    /**
     * 验证回调签名
     */
    public function verifyNotify($params) {
        if (!isset($params['sign'])) {
            return false;
        }
        
        $sign = $params['sign'];
        unset($params['sign'], $params['sign_type']);
        
        $expectedSign = $this->generateSign($params);
        
        return $sign === $expectedSign;
    }
    
    /**
     * 处理支付通知
     */
    public function handleNotify($params) {
        // 验证签名
        if (!$this->verifyNotify($params)) {
            throw new Exception('签名验证失败');
        }
        
        // 检查支付状态
        if ($params['trade_status'] !== 'TRADE_SUCCESS') {
            throw new Exception('支付状态异常：' . $params['trade_status']);
        }
        
        // 解析订单号
        $tradeNo = $params['out_trade_no'];
        if (strpos($tradeNo, 'XY') !== 0) {
            throw new Exception('订单号格式错误');
        }
        
        // 提取订单ID
        $parts = explode('_', $tradeNo);
        $orderIdPart = substr($parts[0], 2); // 移除 'XY' 前缀
        $orderId = intval($orderIdPart);
        
        if ($orderId <= 0) {
            throw new Exception('无效的订单ID');
        }
        
        // 验证订单
        $stmt = $this->pdo->prepare("SELECT * FROM orders WHERE id = ?");
        $stmt->execute([$orderId]);
        $order = $stmt->fetch();
        
        if (!$order) {
            throw new Exception('订单不存在');
        }
        
        if ($order['status'] !== 'pending') {
            throw new Exception('订单状态异常');
        }
        
        // 验证金额
        $expectedAmount = number_format($order['total_price'], 2, '.', '');
        $actualAmount = number_format($params['money'], 2, '.', '');
        
        if ($expectedAmount !== $actualAmount) {
            throw new Exception('支付金额不匹配');
        }
        
        // 更新订单状态
        $this->pdo->beginTransaction();
        
        try {
            $stmt = $this->pdo->prepare("
                UPDATE orders 
                SET status = 'paid', 
                    payment_method = 'epay',
                    payment_time = NOW(),
                    trade_no = ?,
                    updated_at = NOW() 
                WHERE id = ?
            ");
            $stmt->execute([$params['trade_no'] ?? $tradeNo, $orderId]);
            
            $this->pdo->commit();
            
            return [
                'success' => true,
                'order_id' => $orderId,
                'trade_no' => $tradeNo
            ];
            
        } catch (Exception $e) {
            $this->pdo->rollBack();
            throw $e;
        }
    }
    
    /**
     * 查询支付状态
     */
    public function queryPayment($tradeNo) {
        $params = [
            'pid' => $this->config['partner_id'],
            'key' => $this->config['key'],
            'out_trade_no' => $tradeNo
        ];
        
        $queryUrl = $this->config['api_url'] . 'api.php?' . http_build_query($params);
        
        $response = file_get_contents($queryUrl);
        
        if ($response === false) {
            throw new Exception('查询支付状态失败');
        }
        
        $result = json_decode($response, true);
        
        if (!$result) {
            throw new Exception('支付状态查询响应格式错误');
        }
        
        return $result;
    }
    
    /**
     * 获取支付方式列表
     */
    public function getPaymentMethods() {
        return [
            'alipay' => '支付宝',
            'wxpay' => '微信支付',
            'qqpay' => 'QQ钱包',
            'bank' => '网银支付'
        ];
    }
    
    /**
     * 检查配置是否有效
     */
    public function isConfigValid() {
        return !empty($this->config['api_url']) && 
               !empty($this->config['partner_id']) && 
               !empty($this->config['key']);
    }
}
