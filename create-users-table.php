<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建用户表</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .success {
            color: #28a745;
            background: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            text-decoration: none;
            display: inline-block;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 创建用户表</h1>
        
        <?php
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            try {
                // 直接使用数据库配置，避免包含其他文件
                $host = 'localhost';
                $dbname = 'xianyu_db';
                $username_db = 'xianyu_db';
                $password_db = '2CY9SsWpXs6yWHks';
                $charset = 'utf8mb4';
                
                $dsn = "mysql:host=$host;dbname=$dbname;charset=$charset";
                
                $pdo = new PDO($dsn, $username_db, $password_db, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
                ]);
                
                echo '<div class="success">✅ 数据库连接成功</div>';
                
                // 创建users表
                $sql = "CREATE TABLE IF NOT EXISTS users (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50) UNIQUE NOT NULL,
                    email VARCHAR(100) UNIQUE NOT NULL,
                    phone VARCHAR(20) UNIQUE NOT NULL,
                    password VARCHAR(255) NOT NULL,
                    nickname VARCHAR(50),
                    avatar VARCHAR(255) DEFAULT '',
                    status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
                    user_type ENUM('user', 'admin') DEFAULT 'user',
                    remember_token VARCHAR(100) DEFAULT NULL,
                    remember_expires DATETIME DEFAULT NULL,
                    last_login DATETIME DEFAULT NULL,
                    login_count INT DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
                
                $pdo->exec($sql);
                echo '<div class="success">✅ users表创建成功</div>';
                
                // 检查是否已有测试用户
                $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE username IN ('testuser', 'admin')");
                $stmt->execute();
                $userCount = $stmt->fetch()['count'];
                
                if ($userCount == 0) {
                    // 创建测试用户
                    $testUsers = [
                        [
                            'username' => 'testuser',
                            'email' => '<EMAIL>',
                            'phone' => '13800138000',
                            'password' => password_hash('123456', PASSWORD_DEFAULT),
                            'nickname' => '测试用户',
                            'user_type' => 'user'
                        ],
                        [
                            'username' => 'admin',
                            'email' => '<EMAIL>',
                            'phone' => '13800138001',
                            'password' => password_hash('admin123', PASSWORD_DEFAULT),
                            'nickname' => '管理员',
                            'user_type' => 'admin'
                        ]
                    ];
                    
                    $stmt = $pdo->prepare("INSERT INTO users (username, email, phone, password, nickname, user_type) VALUES (?, ?, ?, ?, ?, ?)");
                    
                    foreach ($testUsers as $user) {
                        $stmt->execute([
                            $user['username'],
                            $user['email'],
                            $user['phone'],
                            $user['password'],
                            $user['nickname'],
                            $user['user_type']
                        ]);
                        echo '<div class="success">✅ 创建用户: ' . $user['username'] . '</div>';
                    }
                } else {
                    echo '<div class="success">✅ 测试用户已存在 (' . $userCount . ' 个)</div>';
                }
                
                echo '<div class="success"><strong>🎉 设置完成！</strong></div>';
                echo '<p><strong>测试账号：</strong></p>';
                echo '<ul>';
                echo '<li>用户名: testuser, 密码: 123456</li>';
                echo '<li>管理员: admin, 密码: admin123</li>';
                echo '</ul>';
                
            } catch (PDOException $e) {
                echo '<div class="error">❌ 数据库错误: ' . $e->getMessage() . '</div>';
            } catch (Exception $e) {
                echo '<div class="error">❌ 系统错误: ' . $e->getMessage() . '</div>';
            }
        } else {
            echo '<p>点击下面的按钮创建用户表和测试账号：</p>';
        }
        ?>
        
        <?php if ($_SERVER['REQUEST_METHOD'] !== 'POST'): ?>
        <form method="POST">
            <button type="submit" class="btn">🚀 创建用户表和测试账号</button>
        </form>
        <?php endif; ?>
        
        <div style="margin-top: 20px;">
            <a href="simple-login-test.php" class="btn">测试登录功能</a>
            <a href="system-status.php" class="btn">系统状态</a>
        </div>
    </div>
</body>
</html>
