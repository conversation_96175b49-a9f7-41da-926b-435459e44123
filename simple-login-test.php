<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .form-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-accounts {
            background: #e2e3e5;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="form-container">
        <h2>简单登录测试</h2>
        
        <div class="test-accounts">
            <strong>测试账号：</strong><br>
            用户名: testuser 密码: 123456<br>
            用户名: admin 密码: admin123
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="account">账号:</label>
                <input type="text" id="account" name="account" value="testuser" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" value="123456" required>
            </div>
            
            <button type="submit" id="submitBtn">登录</button>
        </form>
        
        <div id="result" class="result"></div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const resultDiv = document.getElementById('result');
            const formData = new FormData();
            
            // 获取表单数据
            const account = document.getElementById('account').value;
            const password = document.getElementById('password').value;
            
            // 构建请求数据
            formData.append('login_type', 'account');
            formData.append('account', account);
            formData.append('password', password);
            
            // 设置按钮状态
            submitBtn.disabled = true;
            submitBtn.textContent = '登录中...';
            
            // 显示处理状态
            resultDiv.style.display = 'block';
            resultDiv.className = 'result';
            resultDiv.textContent = '正在处理登录请求...';
            
            // 发送请求
            fetch('api/login.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);
                return response.text();
            })
            .then(text => {
                console.log('Raw response:', text);
                
                // 尝试解析JSON
                try {
                    const data = JSON.parse(text);
                    console.log('Parsed data:', data);
                    
                    if (data.success) {
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = '✅ 登录成功！<br>用户: ' + data.user.username + '<br>正在跳转...';
                        
                        setTimeout(() => {
                            window.location.href = data.redirect || 'index.php';
                        }, 2000);
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.innerHTML = '❌ 登录失败: ' + data.message;
                    }
                } catch (parseError) {
                    console.error('JSON parse error:', parseError);
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = '❌ 响应解析错误<br>原始响应: ' + text.substring(0, 200);
                }
            })
            .catch(error => {
                console.error('Network error:', error);
                resultDiv.className = 'result error';
                resultDiv.innerHTML = '❌ 网络错误: ' + error.message;
            })
            .finally(() => {
                // 恢复按钮状态
                submitBtn.disabled = false;
                submitBtn.textContent = '登录';
            });
        });
    </script>
</body>
</html>
