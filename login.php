<?php
$pageTitle = '登录';
require_once 'includes/header.php';

// 如果已登录，重定向到首页
if (isLoggedIn()) {
    redirect('index.php');
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = sanitizeInput($_POST['username']);
    $password = $_POST['password'];
    
    if (empty($username) || empty($password)) {
        $error = '请填写完整的登录信息';
    } else {
        if (login($username, $password)) {
            $_SESSION['success_message'] = '登录成功！';
            redirect('index.php');
        } else {
            $error = '用户名或密码错误';
        }
    }
}
?>

<!-- 主要内容区 -->
<main class="main login-page">
    <div class="container">
        <div class="auth-container">
            <div class="auth-header">
                <h2>登录数字鱼</h2>
                <p>还没有账号？<a href="register.php">立即注册</a></p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <div class="login-tabs">
                <div class="login-tab active" data-tab="password">账号密码登录</div>
                <div class="login-tab" data-tab="phone">手机号登录</div>
            </div>
            
            <!-- 账号密码登录表单 -->
            <form class="auth-form login-form active" id="passwordLoginForm" method="POST" data-form="password">
                <div class="form-group">
                    <label for="loginUsername">账号</label>
                    <input type="text" id="loginUsername" name="username" placeholder="手机号/邮箱/用户名" required value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                </div>
                
                <div class="form-group">
                    <label for="loginPassword">密码</label>
                    <div class="input-group">
                        <input type="password" id="loginPassword" name="password" placeholder="请输入密码" required>
                        <span class="toggle-password"><i class="bi bi-eye"></i></span>
                    </div>
                </div>
                
                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="rememberPassword" checked>
                        <span>记住登录</span>
                    </label>
                    <a href="#" class="forgot-link">忘记密码？</a>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="auth-submit-btn">登录</button>
                </div>
            </form>
            
            <!-- 手机号登录表单 -->
            <form class="auth-form login-form" id="phoneLoginForm" data-form="phone">
                <div class="form-group">
                    <label for="loginPhone">手机号码</label>
                    <div class="input-group">
                        <span class="input-prefix">+86</span>
                        <input type="tel" id="loginPhone" name="phone" placeholder="请输入手机号码" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="loginVerifyCode">验证码</label>
                    <div class="input-group">
                        <input type="text" id="loginVerifyCode" name="verifyCode" placeholder="请输入验证码" required>
                        <button type="button" class="verify-code-btn">获取验证码</button>
                    </div>
                </div>
                
                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="rememberPhone" checked>
                        <span>记住登录</span>
                    </label>
                    <a href="#" class="forgot-link">登录遇到问题？</a>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="auth-submit-btn">登录</button>
                </div>
            </form>
            
            <div class="other-login">
                <p class="divider"><span>其他方式登录</span></p>
                <div class="social-login">
                    <a href="#" class="social-icon"><i class="bi bi-wechat"></i></a>
                    <a href="#" class="social-icon"><i class="bi bi-qq"></i></a>
                    <a href="#" class="social-icon"><i class="bi bi-apple"></i></a>
                    <a href="#" class="social-icon"><i class="bi bi-envelope"></i></a>
                </div>
            </div>
        </div>
    </div>
</main>

<script>
// 切换登录方式
document.querySelectorAll('.login-tab').forEach(tab => {
    tab.addEventListener('click', function() {
        const tabType = this.dataset.tab;
        
        // 更新标签状态
        document.querySelectorAll('.login-tab').forEach(t => t.classList.remove('active'));
        this.classList.add('active');
        
        // 更新表单显示
        document.querySelectorAll('.login-form').forEach(form => {
            form.classList.remove('active');
            if (form.dataset.form === tabType) {
                form.classList.add('active');
            }
        });
    });
});

// 密码显示/隐藏
document.querySelectorAll('.toggle-password').forEach(toggle => {
    toggle.addEventListener('click', function() {
        const input = this.previousElementSibling;
        const icon = this.querySelector('i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'bi bi-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'bi bi-eye';
        }
    });
});

// 表单提交优化
document.querySelectorAll('form').forEach(form => {
    form.addEventListener('submit', function(e) {
        e.preventDefault();

        const submitBtn = this.querySelector('[type="submit"]');
        const formData = new FormData(this);

        // 设置按钮加载状态
        loading.setButtonLoading(submitBtn, true);

        // 模拟登录请求
        fetch('api/login.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            loading.setButtonLoading(submitBtn, false);

            if (data.success) {
                notify.success('登录成功', '正在跳转到首页...');
                setTimeout(() => {
                    window.location.href = data.redirect || 'index.php';
                }, 1500);
            } else {
                notify.error('登录失败', data.message || '用户名或密码错误');

                // 震动效果
                this.style.animation = 'shake 0.5s ease-in-out';
                setTimeout(() => {
                    this.style.animation = '';
                }, 500);
            }
        })
        .catch(error => {
            loading.setButtonLoading(submitBtn, false);
            notify.error('网络错误', '请检查网络连接后重试');
        });
    });
});

// 输入框焦点优化
document.querySelectorAll('input').forEach(input => {
    input.addEventListener('focus', function() {
        this.parentNode.classList.add('focused');
    });

    input.addEventListener('blur', function() {
        if (!this.value.trim()) {
            this.parentNode.classList.remove('focused');
        }
    });

    // 如果有值，保持焦点状态
    if (input.value.trim()) {
        input.parentNode.classList.add('focused');
    }
});

// 添加震动动画
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-10px); }
        75% { transform: translateX(10px); }
    }

    .form-group.focused .form-label {
        color: #007bff;
        transform: translateY(-20px) scale(0.85);
    }

    .form-group .form-label {
        transition: all 0.2s ease;
        transform-origin: left top;
    }
`;
document.head.appendChild(style);
</script>

<?php require_once 'includes/footer.php'; ?>
