<?php
$pageTitle = '注册';
require_once 'includes/header.php';

// 如果已登录，重定向到首页
if (isLoggedIn()) {
    redirect('index.php');
}
?>

<!-- 主要内容区 -->
<main class="main register-page">
    <div class="container">
        <div class="auth-container">
            <div class="auth-header">
                <h2>注册数字鱼账号</h2>
                <p>已有账号？<a href="login.php">立即登录</a></p>
            </div>
            

            
            <form class="auth-form" id="registerForm" method="POST">
                <div class="form-group">
                    <label for="username">用户名（选填）</label>
                    <input type="text" id="username" name="username" placeholder="留空将自动生成" value="<?php echo isset($_POST['username']) ? htmlspecialchars($_POST['username']) : ''; ?>">
                </div>
                
                <div class="form-group">
                    <label for="email">邮箱地址</label>
                    <input type="email" id="email" name="email" placeholder="请输入邮箱地址" required value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                </div>
                
                <div class="form-group">
                    <label for="phone">手机号码</label>
                    <div class="input-group">
                        <span class="input-prefix">+86</span>
                        <input type="tel" id="phone" name="phone" placeholder="请输入手机号码" required value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="password">设置密码</label>
                    <div class="input-group">
                        <input type="password" id="password" name="password" placeholder="请设置登录密码" required>
                        <span class="toggle-password"><i class="bi bi-eye"></i></span>
                    </div>
                    <div class="password-strength">
                        <div class="strength-bar">
                            <div class="strength-level" data-level="0"></div>
                        </div>
                        <div class="strength-text">密码强度：弱</div>
                    </div>
                    <p class="form-tip">密码长度8-20位，必须包含字母、数字</p>
                </div>
                
                <div class="form-group">
                    <label for="confirmPassword">确认密码</label>
                    <div class="input-group">
                        <input type="password" id="confirmPassword" name="confirmPassword" placeholder="请再次输入密码" required>
                        <span class="toggle-password"><i class="bi bi-eye"></i></span>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="nickname">昵称</label>
                    <input type="text" id="nickname" name="nickname" placeholder="请设置昵称" required value="<?php echo isset($_POST['nickname']) ? htmlspecialchars($_POST['nickname']) : ''; ?>">
                </div>
                
                <div class="form-group checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="agreement" required>
                        <span>我已阅读并同意</span>
                    </label>
                    <a href="#" class="agreement-link">《数字鱼用户协议》</a>
                    <a href="#" class="agreement-link">《隐私政策》</a>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="auth-submit-btn">注册</button>
                </div>
                
                <div class="other-login">
                    <p class="divider"><span>其他方式登录</span></p>
                    <div class="social-login">
                        <a href="#" class="social-icon"><i class="bi bi-wechat"></i></a>
                        <a href="#" class="social-icon"><i class="bi bi-qq"></i></a>
                        <a href="#" class="social-icon"><i class="bi bi-apple"></i></a>
                        <a href="#" class="social-icon"><i class="bi bi-envelope"></i></a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</main>

<script>
// 密码显示/隐藏
document.querySelectorAll('.toggle-password').forEach(toggle => {
    toggle.addEventListener('click', function() {
        const input = this.previousElementSibling;
        const icon = this.querySelector('i');
        
        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'bi bi-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'bi bi-eye';
        }
    });
});

// 密码强度检测
document.getElementById('password').addEventListener('input', function() {
    const password = this.value;
    const strengthBar = document.querySelector('.strength-level');
    const strengthText = document.querySelector('.strength-text');
    
    let strength = 0;
    let text = '弱';
    
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    if (strength >= 4) {
        text = '强';
        strengthBar.className = 'strength-level strong';
    } else if (strength >= 2) {
        text = '中';
        strengthBar.className = 'strength-level medium';
    } else {
        text = '弱';
        strengthBar.className = 'strength-level weak';
    }
    
    strengthText.textContent = '密码强度：' + text;
});

// 表单提交处理
document.getElementById('registerForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const submitBtn = this.querySelector('[type="submit"]');
    const formData = new FormData(this);

    // 验证表单
    const username = formData.get('username') || '';
    const email = formData.get('email');
    const phone = formData.get('phone');
    const password = formData.get('password');
    const confirmPassword = formData.get('confirmPassword');
    const nickname = formData.get('nickname');
    const agreement = formData.get('agreement');

    // 客户端验证
    if (!email || !phone || !password || !nickname) {
        if (window.notify) {
            window.notify.error('表单验证失败', '请填写完整的注册信息');
        } else {
            alert('请填写完整的注册信息');
        }
        return;
    }

    if (password !== confirmPassword) {
        if (window.notify) {
            window.notify.error('密码不匹配', '两次输入的密码不一致');
        } else {
            alert('两次输入的密码不一致');
        }
        return;
    }

    if (!agreement) {
        if (window.notify) {
            window.notify.error('请同意协议', '请同意用户协议和隐私政策');
        } else {
            alert('请同意用户协议和隐私政策');
        }
        return;
    }

    // 生成用户名（如果为空）
    if (!username.trim()) {
        const generatedUsername = 'user_' + phone.slice(-4) + Math.floor(Math.random() * 9000 + 1000);
        formData.set('username', generatedUsername);
    }

    // 设置按钮加载状态
    if (window.loading) {
        window.loading.setButtonLoading(submitBtn, true);
    } else {
        submitBtn.disabled = true;
        submitBtn.textContent = '注册中...';
    }

    // 发送注册请求
    fetch('api/register.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // 恢复按钮状态
        if (window.loading) {
            window.loading.setButtonLoading(submitBtn, false);
        } else {
            submitBtn.disabled = false;
            submitBtn.textContent = '注册';
        }

        if (data.success) {
            // 显示成功消息
            if (window.notify) {
                window.notify.success('注册成功', '欢迎加入数字鱼！正在跳转...');
            } else {
                alert('注册成功！欢迎加入数字鱼！');
            }

            // 延迟跳转到首页或登录页
            setTimeout(() => {
                window.location.href = data.redirect || 'index.php';
            }, 2000);
        } else {
            // 显示错误消息
            if (window.notify) {
                window.notify.error('注册失败', data.message || '注册过程中出现错误');
            } else {
                alert('注册失败：' + (data.message || '注册过程中出现错误'));
            }

            // 震动效果
            this.style.animation = 'shake 0.5s ease-in-out';
            setTimeout(() => {
                this.style.animation = '';
            }, 500);
        }
    })
    .catch(error => {
        console.error('Register error:', error);

        // 恢复按钮状态
        if (window.loading) {
            window.loading.setButtonLoading(submitBtn, false);
        } else {
            submitBtn.disabled = false;
            submitBtn.textContent = '注册';
        }

        // 显示错误消息
        if (window.notify) {
            window.notify.error('网络错误', '请检查网络连接后重试');
        } else {
            alert('网络错误，请检查网络连接后重试');
        }
    });
});

// 添加震动动画样式
const style = document.createElement('style');
style.textContent = `
    @keyframes shake {
        0%, 100% { transform: translateX(0); }
        25% { transform: translateX(-10px); }
        75% { transform: translateX(10px); }
    }
`;
document.head.appendChild(style);
</script>

<?php require_once 'includes/footer.php'; ?>
