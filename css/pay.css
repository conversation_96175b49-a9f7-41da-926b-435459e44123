/* 支付页面样式 */

.pay-page {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

.pay-container {
    max-width: 800px;
    margin: 0 auto;
}

/* 页面标题 */
.page-header {
    background: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.page-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.page-title i {
    color: #ff6b35;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #666;
}

.breadcrumb a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: #ff6b35;
}

.breadcrumb i {
    font-size: 12px;
    color: #ccc;
}

/* 支付内容区 */
.pay-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

/* 区块标题 */
.section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 15px 0;
    padding-bottom: 10px;
    border-bottom: 2px solid #ff6b35;
    display: inline-block;
}

/* 订单信息区 */
.order-info-section {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.order-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.order-header {
    background: #f8f9fa;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.order-number {
    font-weight: 600;
    color: #333;
}

.order-time {
    color: #666;
    font-size: 14px;
}

.product-info {
    padding: 20px;
    display: flex;
    gap: 15px;
    align-items: center;
}

.product-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.product-details {
    flex: 1;
}

.product-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
    line-height: 1.4;
}

.seller-info {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.seller-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
}

.seller-name {
    font-size: 14px;
    color: #666;
}

.order-quantity {
    font-size: 14px;
    color: #666;
}

.product-price {
    text-align: right;
}

.unit-price {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.total-price {
    font-size: 18px;
    font-weight: 600;
    color: #ff6b35;
}

/* 支付方式选择 */
.payment-section {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.payment-methods {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.payment-method {
    display: flex;
    align-items: center;
    padding: 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.payment-method:hover {
    border-color: #ff6b35;
    background: #fff8f5;
}

.payment-method.active {
    border-color: #ff6b35;
    background: #fff8f5;
}

.method-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: #666;
}

.payment-method.active .method-icon {
    background: #ff6b35;
    color: white;
}

.method-info {
    flex: 1;
}

.method-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.method-desc {
    font-size: 14px;
    color: #666;
}

.method-radio {
    margin-left: 15px;
}

.method-radio input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: #ff6b35;
}

/* 支付金额确认 */
.payment-summary {
    background: white;
    padding: 20px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
}

.summary-row:last-child {
    border-bottom: none;
}

.summary-row.total {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    border-top: 2px solid #e9ecef;
    margin-top: 10px;
    padding-top: 15px;
}

.summary-row .label {
    color: #666;
}

.summary-row .value {
    color: #333;
    font-weight: 500;
}

.summary-row .value.free {
    color: #28a745;
}

.summary-row.total .value {
    color: #ff6b35;
    font-size: 20px;
}

/* 支付按钮 */
.payment-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    padding: 20px 0;
}

.btn-cancel,
.btn-pay {
    padding: 12px 30px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 140px;
}

.btn-cancel {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e9ecef;
}

.btn-cancel:hover {
    background: #e9ecef;
    color: #333;
}

.btn-pay {
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
}

.btn-pay:hover {
    background: linear-gradient(135deg, #ff5722, #ff6b35);
    box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
    transform: translateY(-2px);
}

/* 支付处理模态框 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: white;
    padding: 40px;
    border-radius: 16px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    min-width: 300px;
}

.payment-processing {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
}

.processing-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b35, #ff8c42);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    animation: pulse 2s infinite;
}

.payment-processing h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.payment-processing p {
    margin: 0;
    color: #666;
    font-size: 14px;
}

.loading-spinner {
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #ff6b35;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* 动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.7);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(255, 107, 53, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(255, 107, 53, 0);
    }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 消息提示 */
.message-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1001;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.message-toast.show {
    transform: translateX(0);
}

.message-toast.message-success {
    background: #28a745;
}

.message-toast.message-error {
    background: #dc3545;
}

.message-toast.message-info {
    background: #17a2b8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .pay-container {
        margin: 0 15px;
    }
    
    .product-info {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }
    
    .product-price {
        text-align: center;
    }
    
    .payment-actions {
        flex-direction: column;
    }
    
    .btn-cancel,
    .btn-pay {
        width: 100%;
    }
}
