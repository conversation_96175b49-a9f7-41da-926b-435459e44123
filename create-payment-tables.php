<?php
/**
 * 创建支付配置表
 */
require_once 'includes/functions.php';

header('Content-Type: application/json; charset=utf-8');

try {
    $createdTables = [];
    $errors = [];
    
    // 创建支付配置表
    $sql = "CREATE TABLE IF NOT EXISTS payment_config (
        id INT AUTO_INCREMENT PRIMARY KEY,
        payment_method VARCHAR(50) NOT NULL UNIQUE,
        display_name VARCHAR(100) NOT NULL,
        is_enabled BOOLEAN DEFAULT FALSE,
        config_data JSON,
        sort_order INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        INDEX idx_payment_method (payment_method),
        INDEX idx_enabled (is_enabled),
        INDEX idx_sort_order (sort_order)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    if ($pdo->exec($sql) !== false) {
        $createdTables[] = 'payment_config';
    }
    
    // 检查是否已有数据
    $stmt = $pdo->query("SELECT COUNT(*) FROM payment_config");
    $count = $stmt->fetchColumn();
    
    if ($count == 0) {
        // 插入默认支付方式配置
        $defaultConfigs = [
            [
                'payment_method' => 'alipay',
                'display_name' => '支付宝',
                'is_enabled' => 1,
                'config_data' => json_encode([
                    'app_id' => '',
                    'private_key' => '',
                    'public_key' => '',
                    'sandbox' => true
                ]),
                'sort_order' => 1
            ],
            [
                'payment_method' => 'wechat',
                'display_name' => '微信支付',
                'is_enabled' => 1,
                'config_data' => json_encode([
                    'app_id' => '',
                    'mch_id' => '',
                    'key' => '',
                    'sandbox' => true
                ]),
                'sort_order' => 2
            ],
            [
                'payment_method' => 'bank',
                'display_name' => '银行卡',
                'is_enabled' => 1,
                'config_data' => json_encode([
                    'merchant_id' => '',
                    'key' => '',
                    'sandbox' => true
                ]),
                'sort_order' => 3
            ],
            [
                'payment_method' => 'epay',
                'display_name' => '易支付',
                'is_enabled' => 0,
                'config_data' => json_encode([
                    'api_url' => '',
                    'partner_id' => '',
                    'key' => '',
                    'return_url' => (SITE_URL ?? 'http://localhost/xianyu') . '/api/epay-return.php',
                    'notify_url' => (SITE_URL ?? 'http://localhost/xianyu') . '/api/epay-notify.php',
                    'sandbox' => true
                ]),
                'sort_order' => 4
            ]
        ];
        
        $stmt = $pdo->prepare("
            INSERT INTO payment_config (payment_method, display_name, is_enabled, config_data, sort_order) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        foreach ($defaultConfigs as $config) {
            $stmt->execute([
                $config['payment_method'],
                $config['display_name'],
                $config['is_enabled'],
                $config['config_data'],
                $config['sort_order']
            ]);
        }
        
        $createdTables[] = '默认支付配置数据';
    }
    
    // 检查并添加orders表的支付相关字段
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM orders WHERE Field = 'payment_method'");
        if (!$stmt || !$stmt->fetch()) {
            $pdo->exec("ALTER TABLE orders ADD COLUMN payment_method VARCHAR(50) DEFAULT NULL AFTER status");
            $createdTables[] = 'orders.payment_method (字段)';
        }
    } catch (Exception $e) {
        $errors[] = "添加payment_method字段失败: " . $e->getMessage();
    }
    
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM orders WHERE Field = 'payment_time'");
        if (!$stmt || !$stmt->fetch()) {
            $pdo->exec("ALTER TABLE orders ADD COLUMN payment_time TIMESTAMP NULL AFTER payment_method");
            $createdTables[] = 'orders.payment_time (字段)';
        }
    } catch (Exception $e) {
        $errors[] = "添加payment_time字段失败: " . $e->getMessage();
    }
    
    try {
        $stmt = $pdo->query("SHOW COLUMNS FROM orders WHERE Field = 'trade_no'");
        if (!$stmt || !$stmt->fetch()) {
            $pdo->exec("ALTER TABLE orders ADD COLUMN trade_no VARCHAR(100) DEFAULT NULL AFTER payment_time");
            $createdTables[] = 'orders.trade_no (字段)';
        }
    } catch (Exception $e) {
        $errors[] = "添加trade_no字段失败: " . $e->getMessage();
    }
    
    // 确保logs目录存在
    if (!is_dir('logs')) {
        if (mkdir('logs', 0755, true)) {
            $createdTables[] = 'logs目录';
        }
    }
    
    echo json_encode([
        'success' => count($createdTables) > 0,
        'created_tables' => $createdTables,
        'errors' => $errors,
        'message' => count($createdTables) > 0 ? '支付配置表创建成功' : '没有新表需要创建'
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '创建支付配置表失败：' . $e->getMessage(),
        'errors' => [$e->getMessage()]
    ], JSON_UNESCAPED_UNICODE);
}
