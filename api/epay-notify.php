<?php
/**
 * 易支付异步通知处理
 */

// 禁用错误输出
error_reporting(0);
ini_set('display_errors', 0);

require_once '../config/database.php';
require_once '../includes/epay.php';

// 记录日志函数
function logNotify($message, $data = null) {
    $logFile = '../logs/epay_notify.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message";
    
    if ($data) {
        $logMessage .= " Data: " . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    $logMessage .= "\n";
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

try {
    // 记录收到的通知
    logNotify('收到易支付通知', $_POST);
    
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        logNotify('错误：非POST请求');
        exit('fail');
    }
    
    // 检查必要参数
    $requiredParams = ['pid', 'trade_no', 'out_trade_no', 'trade_status', 'money', 'sign'];
    foreach ($requiredParams as $param) {
        if (!isset($_POST[$param]) || $_POST[$param] === '') {
            logNotify("错误：缺少必要参数 $param");
            exit('fail');
        }
    }
    
    // 创建易支付服务实例
    $epayService = new EPayService($pdo);
    
    // 处理支付通知
    $result = $epayService->handleNotify($_POST);
    
    if ($result['success']) {
        logNotify('支付通知处理成功', $result);
        
        // 可以在这里添加其他业务逻辑
        // 比如发送邮件通知、更新用户积分等
        
        echo 'success';
    } else {
        logNotify('支付通知处理失败', $result);
        echo 'fail';
    }
    
} catch (Exception $e) {
    logNotify('支付通知处理异常：' . $e->getMessage(), $_POST);
    echo 'fail';
}
