<?php
/**
 * 用户登录API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit;
}

require_once '../includes/config.php';
require_once '../includes/functions.php';

try {
    // 获取POST数据
    $loginType = $_POST['login_type'] ?? 'account';
    $account = trim($_POST['account'] ?? '');
    $password = $_POST['password'] ?? '';
    $phone = trim($_POST['phone'] ?? '');
    $smsCode = $_POST['sms_code'] ?? '';
    $remember = isset($_POST['remember']);

    // 验证输入
    if ($loginType === 'account') {
        if (empty($account)) {
            throw new Exception('请输入用户名或邮箱');
        }
        if (empty($password)) {
            throw new Exception('请输入密码');
        }
    } elseif ($loginType === 'phone') {
        if (empty($phone)) {
            throw new Exception('请输入手机号');
        }
        if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
            throw new Exception('手机号格式不正确');
        }
        if (empty($smsCode)) {
            throw new Exception('请输入验证码');
        }
    } else {
        throw new Exception('无效的登录方式');
    }

    // 连接数据库
    $pdo = new PDO($dsn, $username, $password_db, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);

    if ($loginType === 'account') {
        // 账号密码登录
        $user = loginWithAccount($pdo, $account, $password);
    } else {
        // 手机验证码登录
        $user = loginWithPhone($pdo, $phone, $smsCode);
    }

    if (!$user) {
        throw new Exception('登录失败，请检查您的登录信息');
    }

    // 检查用户状态
    if ($user['status'] !== 'active') {
        throw new Exception('账户已被禁用，请联系客服');
    }

    // 设置会话
    session_start();
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['email'] = $user['email'];
    $_SESSION['phone'] = $user['phone'];
    $_SESSION['avatar'] = $user['avatar'];
    $_SESSION['user_type'] = $user['user_type'];
    $_SESSION['login_time'] = time();

    // 记住登录状态
    if ($remember) {
        $token = generateRememberToken();
        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/', '', false, true);
        
        // 保存记住令牌到数据库
        $stmt = $pdo->prepare("UPDATE users SET remember_token = ?, remember_expires = ? WHERE id = ?");
        $stmt->execute([$token, date('Y-m-d H:i:s', time() + (30 * 24 * 60 * 60)), $user['id']]);
    }

    // 更新最后登录时间
    $stmt = $pdo->prepare("UPDATE users SET last_login = NOW(), login_count = login_count + 1 WHERE id = ?");
    $stmt->execute([$user['id']]);

    // 记录登录日志
    logUserAction($pdo, $user['id'], 'login', '用户登录', [
        'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
        'login_type' => $loginType
    ]);

    // 返回成功响应
    echo json_encode([
        'success' => true,
        'message' => '登录成功',
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'phone' => $user['phone'],
            'avatar' => $user['avatar'],
            'user_type' => $user['user_type']
        ],
        'redirect' => $_POST['redirect'] ?? 'index.php'
    ]);

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 账号密码登录
 */
function loginWithAccount($pdo, $account, $password) {
    // 查找用户（支持用户名、邮箱、手机号登录）
    $stmt = $pdo->prepare("
        SELECT * FROM users 
        WHERE (username = ? OR email = ? OR phone = ?) 
        AND deleted_at IS NULL
    ");
    $stmt->execute([$account, $account, $account]);
    $user = $stmt->fetch();

    if (!$user) {
        return false;
    }

    // 验证密码
    if (!password_verify($password, $user['password'])) {
        return false;
    }

    return $user;
}

/**
 * 手机验证码登录
 */
function loginWithPhone($pdo, $phone, $smsCode) {
    // 验证短信验证码
    if (!verifySmsCode($pdo, $phone, $smsCode, 'login')) {
        throw new Exception('验证码错误或已过期');
    }

    // 查找用户
    $stmt = $pdo->prepare("SELECT * FROM users WHERE phone = ? AND deleted_at IS NULL");
    $stmt->execute([$phone]);
    $user = $stmt->fetch();

    if (!$user) {
        throw new Exception('手机号未注册');
    }

    return $user;
}

/**
 * 生成记住登录令牌
 */
function generateRememberToken() {
    return bin2hex(random_bytes(32));
}

/**
 * 验证短信验证码
 */
function verifySmsCode($pdo, $phone, $code, $type = 'login') {
    $stmt = $pdo->prepare("
        SELECT * FROM sms_codes 
        WHERE phone = ? AND code = ? AND type = ? AND used = 0 
        AND expires_at > NOW() 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$phone, $code, $type]);
    $smsRecord = $stmt->fetch();

    if (!$smsRecord) {
        return false;
    }

    // 标记验证码为已使用
    $stmt = $pdo->prepare("UPDATE sms_codes SET used = 1 WHERE id = ?");
    $stmt->execute([$smsRecord['id']]);

    return true;
}

/**
 * 记录用户操作日志
 */
function logUserAction($pdo, $userId, $action, $description, $data = []) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO user_logs (user_id, action, description, data, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $userId,
            $action,
            $description,
            json_encode($data),
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        // 日志记录失败不影响主流程
        error_log('Failed to log user action: ' . $e->getMessage());
    }
}
?>
