<?php
/**
 * 数据库修复脚本
 * 修复现有表结构，确保登录注册功能正常
 */

// 清理输出缓冲区
ob_clean();

require_once '../includes/config.php';

try {
    $pdo = new PDO($dsn, $username_db, $password_db, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);

    echo "<h2>🔧 数据库修复工具</h2>";

    // 检查users表是否存在
    $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
    if (!$stmt->fetch()) {
        echo "<p>❌ users表不存在，正在创建...</p>";
        
        // 创建users表
        $sql = "CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) UNIQUE NOT NULL,
            email VARCHAR(100) UNIQUE NOT NULL,
            phone VARCHAR(20) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            nickname VARCHAR(50),
            avatar VARCHAR(255) DEFAULT '',
            status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
            user_type ENUM('user', 'admin') DEFAULT 'user',
            remember_token VARCHAR(100) DEFAULT NULL,
            remember_expires DATETIME DEFAULT NULL,
            last_login DATETIME DEFAULT NULL,
            login_count INT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($sql);
        echo "<p>✅ users表创建成功</p>";
    } else {
        echo "<p>✅ users表已存在</p>";
        
        // 检查并添加缺失的字段
        $stmt = $pdo->query("DESCRIBE users");
        $columns = [];
        while ($row = $stmt->fetch()) {
            $columns[] = $row['Field'];
        }
        
        $requiredColumns = [
            'nickname' => "ALTER TABLE users ADD COLUMN nickname VARCHAR(50) AFTER password",
            'avatar' => "ALTER TABLE users ADD COLUMN avatar VARCHAR(255) DEFAULT '' AFTER nickname",
            'status' => "ALTER TABLE users ADD COLUMN status ENUM('active', 'inactive', 'banned') DEFAULT 'active' AFTER avatar",
            'user_type' => "ALTER TABLE users ADD COLUMN user_type ENUM('user', 'admin') DEFAULT 'user' AFTER status",
            'remember_token' => "ALTER TABLE users ADD COLUMN remember_token VARCHAR(100) DEFAULT NULL AFTER user_type",
            'remember_expires' => "ALTER TABLE users ADD COLUMN remember_expires DATETIME DEFAULT NULL AFTER remember_token",
            'last_login' => "ALTER TABLE users ADD COLUMN last_login DATETIME DEFAULT NULL AFTER remember_expires",
            'login_count' => "ALTER TABLE users ADD COLUMN login_count INT DEFAULT 0 AFTER last_login"
        ];
        
        foreach ($requiredColumns as $column => $sql) {
            if (!in_array($column, $columns)) {
                try {
                    $pdo->exec($sql);
                    echo "<p>✅ 添加字段: $column</p>";
                } catch (Exception $e) {
                    echo "<p>⚠️ 添加字段 $column 失败: " . $e->getMessage() . "</p>";
                }
            }
        }
    }

    // 检查并创建测试用户
    $stmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE username IN ('testuser', 'admin')");
    $stmt->execute();
    $userCount = $stmt->fetch()['count'];

    if ($userCount == 0) {
        echo "<p>📝 创建测试用户...</p>";
        
        // 创建测试用户
        $testUsers = [
            [
                'username' => 'testuser',
                'email' => '<EMAIL>',
                'phone' => '13800138000',
                'password' => password_hash('123456', PASSWORD_DEFAULT),
                'nickname' => '测试用户',
                'user_type' => 'user'
            ],
            [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'phone' => '13800138001',
                'password' => password_hash('admin123', PASSWORD_DEFAULT),
                'nickname' => '管理员',
                'user_type' => 'admin'
            ]
        ];

        $stmt = $pdo->prepare("INSERT INTO users (username, email, phone, password, nickname, user_type) VALUES (?, ?, ?, ?, ?, ?)");
        
        foreach ($testUsers as $user) {
            try {
                $stmt->execute([
                    $user['username'],
                    $user['email'],
                    $user['phone'],
                    $user['password'],
                    $user['nickname'],
                    $user['user_type']
                ]);
                echo "<p>✅ 创建用户: " . $user['username'] . "</p>";
            } catch (Exception $e) {
                echo "<p>⚠️ 创建用户 " . $user['username'] . " 失败: " . $e->getMessage() . "</p>";
            }
        }
    } else {
        echo "<p>✅ 测试用户已存在 ($userCount 个)</p>";
    }

    // 创建短信验证码表（如果不存在）
    $stmt = $pdo->query("SHOW TABLES LIKE 'sms_codes'");
    if (!$stmt->fetch()) {
        echo "<p>📝 创建短信验证码表...</p>";
        
        $sql = "CREATE TABLE sms_codes (
            id INT AUTO_INCREMENT PRIMARY KEY,
            phone VARCHAR(20) NOT NULL,
            code VARCHAR(10) NOT NULL,
            type ENUM('register', 'login', 'reset', 'verify') DEFAULT 'register',
            used TINYINT(1) DEFAULT 0,
            expires_at DATETIME NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            INDEX idx_phone_type (phone, type),
            INDEX idx_expires (expires_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $pdo->exec($sql);
        echo "<p>✅ 短信验证码表创建成功</p>";
    } else {
        echo "<p>✅ 短信验证码表已存在</p>";
    }

    echo "<br><h3>🎉 数据库修复完成！</h3>";
    echo "<p><strong>测试账号信息：</strong></p>";
    echo "<ul>";
    echo "<li>用户名: testuser, 密码: 123456</li>";
    echo "<li>管理员: admin, 密码: admin123</li>";
    echo "<li>短信验证码: 123456 (开发环境固定)</li>";
    echo "</ul>";
    
    echo "<p><a href='../simple-login-test.php' style='background:#007bff;color:white;padding:10px 20px;text-decoration:none;border-radius:4px;'>测试登录功能</a></p>";

} catch (PDOException $e) {
    echo "<p>❌ 数据库错误: " . $e->getMessage() . "</p>";
} catch (Exception $e) {
    echo "<p>❌ 系统错误: " . $e->getMessage() . "</p>";
}
?>
