<?php
// 订单操作API - 支付、取消、确认收货等
session_start();

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 清理输出缓冲区
while (ob_get_level()) {
    ob_end_clean();
}

// 基本错误处理
ini_set('display_errors', 0);
error_reporting(E_ALL);

// 检查请求方法
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => '只支持POST请求']);
    exit;
}

try {
    // 数据库配置
    $host = 'localhost';
    $dbname = 'xianyu_db';
    $username = 'xianyu_db';
    $password = '2CY9SsWpXs6yWHks';
    
    // 创建数据库连接
    $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 检查登录状态
    if (!isset($_SESSION['user_id'])) {
        echo json_encode(['success' => false, 'message' => '请先登录']);
        exit;
    }
    
    // 获取参数
    $action = $_POST['action'] ?? '';
    $orderId = intval($_POST['order_id'] ?? 0);
    
    if ($orderId <= 0) {
        echo json_encode(['success' => false, 'message' => '订单ID无效']);
        exit;
    }
    
    // 获取订单信息
    $stmt = $pdo->prepare("
        SELECT o.*, p.title as product_title, p.stock as product_stock
        FROM orders o
        JOIN products p ON o.product_id = p.id
        WHERE o.id = ? AND o.buyer_id = ?
    ");
    $stmt->execute([$orderId, $_SESSION['user_id']]);
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        echo json_encode(['success' => false, 'message' => '订单不存在或无权限操作']);
        exit;
    }
    
    // 开始事务
    $pdo->beginTransaction();
    
    switch ($action) {
        case 'cancel':
            // 取消订单
            if ($order['status'] !== 'pending') {
                $pdo->rollBack();
                echo json_encode(['success' => false, 'message' => '只能取消待付款订单']);
                exit;
            }
            
            // 更新订单状态
            $stmt = $pdo->prepare("UPDATE orders SET status = 'cancelled', updated_at = NOW() WHERE id = ?");
            $stmt->execute([$orderId]);
            
            // 恢复商品库存
            $stmt = $pdo->prepare("UPDATE products SET stock = stock + ? WHERE id = ?");
            $stmt->execute([$order['quantity'], $order['product_id']]);
            
            $pdo->commit();
            echo json_encode([
                'success' => true,
                'message' => '订单已取消',
                'new_status' => 'cancelled'
            ]);
            break;
            
        case 'pay':
            // 处理支付
            if ($order['status'] !== 'pending') {
                $pdo->rollBack();
                echo json_encode(['success' => false, 'message' => '订单状态不正确']);
                exit;
            }

            $paymentMethod = $_POST['payment_method'] ?? '';

            // 根据支付方式处理
            if ($paymentMethod === 'epay') {
                // 易支付处理
                try {
                    require_once '../includes/epay.php';
                    $epayService = new EPayService($pdo);

                    if (!$epayService->isConfigValid()) {
                        $pdo->rollBack();
                        echo json_encode(['success' => false, 'message' => '易支付配置无效']);
                        exit;
                    }

                    $paymentResult = $epayService->createPayment(
                        $orderId,
                        $order['total_price'],
                        $order['product_title'] ?? '商品购买'
                    );

                    $pdo->commit();
                    echo json_encode([
                        'success' => true,
                        'message' => '正在跳转到支付页面...',
                        'payment_type' => 'redirect',
                        'payment_url' => $paymentResult['pay_url'],
                        'trade_no' => $paymentResult['trade_no']
                    ]);

                } catch (Exception $e) {
                    $pdo->rollBack();
                    echo json_encode(['success' => false, 'message' => '易支付处理失败：' . $e->getMessage()]);
                }
            } else {
                // 其他支付方式（模拟支付）
                $stmt = $pdo->prepare("UPDATE orders SET status = 'paid', payment_method = ?, updated_at = NOW() WHERE id = ?");
                $stmt->execute([$paymentMethod, $orderId]);

                $pdo->commit();
                echo json_encode([
                    'success' => true,
                    'message' => '支付成功',
                    'new_status' => 'paid'
                ]);
            }
            break;
            
        case 'confirm_received':
            // 确认收货
            if ($order['status'] !== 'paid') {
                $pdo->rollBack();
                echo json_encode(['success' => false, 'message' => '只能确认已付款订单的收货']);
                exit;
            }
            
            // 更新订单状态为已完成
            $stmt = $pdo->prepare("UPDATE orders SET status = 'completed', updated_at = NOW() WHERE id = ?");
            $stmt->execute([$orderId]);
            
            $pdo->commit();
            echo json_encode([
                'success' => true,
                'message' => '确认收货成功',
                'new_status' => 'completed'
            ]);
            break;
            
        case 'request_refund':
            // 申请退款
            if (!in_array($order['status'], ['paid', 'completed'])) {
                $pdo->rollBack();
                echo json_encode(['success' => false, 'message' => '当前订单状态不支持退款']);
                exit;
            }
            
            // 更新订单状态为退款中
            $stmt = $pdo->prepare("UPDATE orders SET status = 'refunding', updated_at = NOW() WHERE id = ?");
            $stmt->execute([$orderId]);
            
            $pdo->commit();
            echo json_encode([
                'success' => true,
                'message' => '退款申请已提交，请等待处理',
                'new_status' => 'refunding'
            ]);
            break;
            
        default:
            echo json_encode(['success' => false, 'message' => '无效的操作']);
            break;
    }
    
} catch (Exception $e) {
    // 回滚事务
    if (isset($pdo) && $pdo->inTransaction()) {
        $pdo->rollBack();
    }
    
    // 记录错误
    error_log("Order action error: " . $e->getMessage());
    
    // 返回错误响应
    echo json_encode([
        'success' => false,
        'message' => '操作失败：' . $e->getMessage(),
        'error_details' => [
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]
    ]);
}

exit;
?>
