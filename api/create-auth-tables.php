<?php
/**
 * 创建用户认证相关数据表
 */

require_once '../includes/config.php';

try {
    $pdo = new PDO($dsn, $username_db, $password_db, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);

    // 创建用户表
    $sql = "CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(50) UNIQUE NOT NULL,
        email VARCHAR(100) UNIQUE NOT NULL,
        phone VARCHAR(20) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        nickname VARCHAR(50),
        avatar VARCHAR(255) DEFAULT '',
        status ENUM('active', 'inactive', 'banned') DEFAULT 'active',
        user_type ENUM('user', 'admin') DEFAULT 'user',
        remember_token VARCHAR(100) DEFAULT NULL,
        remember_expires DATETIME DEFAULT NULL,
        last_login DATETIME DEFAULT NULL,
        login_count INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        deleted_at TIMESTAMP NULL DEFAULT NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ 用户表创建成功<br>";

    // 创建用户资料表
    $sql = "CREATE TABLE IF NOT EXISTS user_profiles (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        nickname VARCHAR(50),
        bio TEXT,
        location VARCHAR(100),
        website VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ 用户资料表创建成功<br>";

    // 创建短信验证码表
    $sql = "CREATE TABLE IF NOT EXISTS sms_codes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        phone VARCHAR(20) NOT NULL,
        code VARCHAR(10) NOT NULL,
        type ENUM('register', 'login', 'reset', 'verify') DEFAULT 'register',
        used TINYINT(1) DEFAULT 0,
        expires_at DATETIME NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX idx_phone_type (phone, type),
        INDEX idx_expires (expires_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ 短信验证码表创建成功<br>";

    // 创建用户操作日志表
    $sql = "CREATE TABLE IF NOT EXISTS user_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        action VARCHAR(50) NOT NULL,
        description TEXT,
        data JSON,
        ip_address VARCHAR(45),
        user_agent TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        INDEX idx_user_action (user_id, action),
        INDEX idx_created (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ 用户日志表创建成功<br>";

    // 创建分类表（如果不存在）
    $sql = "CREATE TABLE IF NOT EXISTS categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL,
        slug VARCHAR(100) UNIQUE NOT NULL,
        description TEXT,
        icon VARCHAR(50),
        sort_order INT DEFAULT 0,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ 分类表创建成功<br>";

    // 插入默认分类数据
    $categories = [
        ['name' => '会员账号', 'slug' => 'membership', 'icon' => 'person-badge'],
        ['name' => '游戏点卡', 'slug' => 'gamecard', 'icon' => 'controller'],
        ['name' => '软件授权', 'slug' => 'software', 'icon' => 'laptop'],
        ['name' => '电子书籍', 'slug' => 'ebooks', 'icon' => 'book'],
        ['name' => '设计素材', 'slug' => 'design', 'icon' => 'palette'],
        ['name' => '在线课程', 'slug' => 'courses', 'icon' => 'mortarboard'],
        ['name' => '音乐影视', 'slug' => 'media', 'icon' => 'music-note-beamed'],
        ['name' => '虚拟服务', 'slug' => 'services', 'icon' => 'gear'],
        ['name' => '其他商品', 'slug' => 'others', 'icon' => 'box']
    ];

    $stmt = $pdo->prepare("INSERT IGNORE INTO categories (name, slug, icon, sort_order) VALUES (?, ?, ?, ?)");
    foreach ($categories as $index => $category) {
        $stmt->execute([$category['name'], $category['slug'], $category['icon'], $index + 1]);
    }
    echo "✅ 默认分类数据插入成功<br>";

    // 创建商品表（如果不存在）
    $sql = "CREATE TABLE IF NOT EXISTS products (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        category_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        price DECIMAL(10,2) NOT NULL,
        original_price DECIMAL(10,2) DEFAULT NULL,
        images JSON,
        status ENUM('active', 'inactive', 'sold') DEFAULT 'active',
        stock INT DEFAULT 1,
        views INT DEFAULT 0,
        likes INT DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES categories(id),
        INDEX idx_status (status),
        INDEX idx_category (category_id),
        INDEX idx_user (user_id),
        INDEX idx_created (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ 商品表创建成功<br>";

    // 创建虚拟商品属性表
    $sql = "CREATE TABLE IF NOT EXISTS virtual_attributes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        product_id INT NOT NULL,
        delivery_method ENUM('auto', 'manual') DEFAULT 'manual',
        delivery_content TEXT,
        usage_instructions TEXT,
        validity_period VARCHAR(100),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ 虚拟商品属性表创建成功<br>";

    // 创建浏览历史表
    $sql = "CREATE TABLE IF NOT EXISTS browse_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        product_id INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
        UNIQUE KEY unique_user_product (user_id, product_id),
        INDEX idx_user_created (user_id, created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $pdo->exec($sql);
    echo "✅ 浏览历史表创建成功<br>";

    // 创建测试用户
    $testUsers = [
        [
            'username' => 'testuser',
            'email' => '<EMAIL>',
            'phone' => '13800138000',
            'password' => password_hash('123456', PASSWORD_DEFAULT),
            'nickname' => '测试用户'
        ],
        [
            'username' => 'admin',
            'email' => '<EMAIL>',
            'phone' => '13800138001',
            'password' => password_hash('admin123', PASSWORD_DEFAULT),
            'nickname' => '管理员',
            'user_type' => 'admin'
        ]
    ];

    $stmt = $pdo->prepare("INSERT IGNORE INTO users (username, email, phone, password, nickname, user_type) VALUES (?, ?, ?, ?, ?, ?)");
    foreach ($testUsers as $user) {
        $userType = $user['user_type'] ?? 'user';
        $stmt->execute([
            $user['username'],
            $user['email'],
            $user['phone'],
            $user['password'],
            $user['nickname'],
            $userType
        ]);
    }
    echo "✅ 测试用户创建成功<br>";

    echo "<br><strong>🎉 所有数据表创建完成！</strong><br>";
    echo "<br>测试账号：<br>";
    echo "用户名: testuser, 密码: 123456<br>";
    echo "管理员: admin, 密码: admin123<br>";

} catch (PDOException $e) {
    echo "❌ 数据库错误: " . $e->getMessage();
} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage();
}
?>
