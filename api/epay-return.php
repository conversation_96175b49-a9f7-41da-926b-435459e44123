<?php
/**
 * 易支付同步回调处理
 */

require_once '../config/database.php';
require_once '../includes/epay.php';
require_once '../includes/functions.php';

// 记录日志函数
function logReturn($message, $data = null) {
    $logFile = '../logs/epay_return.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message";
    
    if ($data) {
        $logMessage .= " Data: " . json_encode($data, JSON_UNESCAPED_UNICODE);
    }
    
    $logMessage .= "\n";
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

try {
    // 记录回调参数
    logReturn('收到易支付同步回调', $_GET);
    
    // 检查必要参数
    if (!isset($_GET['out_trade_no']) || !isset($_GET['trade_status'])) {
        logReturn('错误：缺少必要参数');
        $_SESSION['error_message'] = '支付回调参数错误';
        redirect('my-orders.php');
    }
    
    $tradeNo = $_GET['out_trade_no'];
    $tradeStatus = $_GET['trade_status'];
    
    // 解析订单号
    if (strpos($tradeNo, 'XY') !== 0) {
        logReturn('错误：订单号格式错误', ['trade_no' => $tradeNo]);
        $_SESSION['error_message'] = '订单号格式错误';
        redirect('my-orders.php');
    }
    
    // 提取订单ID
    $parts = explode('_', $tradeNo);
    $orderIdPart = substr($parts[0], 2); // 移除 'XY' 前缀
    $orderId = intval($orderIdPart);
    
    if ($orderId <= 0) {
        logReturn('错误：无效的订单ID', ['order_id' => $orderId]);
        $_SESSION['error_message'] = '无效的订单ID';
        redirect('my-orders.php');
    }
    
    // 验证订单存在
    $stmt = $pdo->prepare("SELECT * FROM orders WHERE id = ?");
    $stmt->execute([$orderId]);
    $order = $stmt->fetch();
    
    if (!$order) {
        logReturn('错误：订单不存在', ['order_id' => $orderId]);
        $_SESSION['error_message'] = '订单不存在';
        redirect('my-orders.php');
    }
    
    // 检查支付状态
    if ($tradeStatus === 'TRADE_SUCCESS') {
        // 支付成功
        logReturn('支付成功', ['order_id' => $orderId, 'trade_no' => $tradeNo]);
        $_SESSION['success_message'] = '支付成功！订单已完成';
        redirect('my-orders.php?status=paid');
    } else {
        // 支付失败或取消
        logReturn('支付失败或取消', ['order_id' => $orderId, 'trade_status' => $tradeStatus]);
        $_SESSION['error_message'] = '支付失败或已取消';
        redirect('pay.php?order_id=' . $orderId);
    }
    
} catch (Exception $e) {
    logReturn('同步回调处理异常：' . $e->getMessage(), $_GET);
    $_SESSION['error_message'] = '支付回调处理失败';
    redirect('my-orders.php');
}
