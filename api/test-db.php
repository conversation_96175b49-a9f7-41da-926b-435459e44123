<?php
// 直接包含数据库配置，避免其他文件的输出
require_once '../config/database.php';

// 清理任何之前的输出
ob_clean();

// 设置JSON响应头
header('Content-Type: application/json; charset=utf-8');

try {
    // 检查新增的表是否存在
    $tables = ['cart', 'wallet', 'wallet_transactions', 'verification'];
    $existingTables = [];
    $missingTables = [];
    
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");

        if ($stmt->fetch()) {
            $existingTables[] = $table;
        } else {
            $missingTables[] = $table;
        }
    }
    
    // 检查uploads目录权限
    $uploadsPath = '../uploads/';
    $uploadsWritable = is_writable($uploadsPath);
    
    // 检查基本表结构
    $basicTables = ['users', 'products', 'categories'];
    $basicTablesExist = [];
    
    foreach ($basicTables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");

        if ($stmt->fetch()) {
            $basicTablesExist[] = $table;
        }
    }
    
    $response = [
        'success' => count($missingTables) === 0,
        'tables' => $existingTables,
        'missing_tables' => $missingTables,
        'basic_tables' => $basicTablesExist,
        'uploads_writable' => $uploadsWritable,
        'uploads_path' => $uploadsPath,
        'message' => count($missingTables) === 0 ? '所有表都已创建' : '缺少部分表'
    ];
    
    echo json_encode($response, JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => '数据库检查失败：' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

// 确保没有额外输出
exit;
