<?php
/**
 * 用户注册API
 */

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit;
}

require_once '../includes/config.php';
require_once '../includes/functions.php';

try {
    // 获取POST数据
    $username = trim($_POST['username'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirmPassword = $_POST['confirm_password'] ?? '';
    $smsCode = $_POST['sms_code'] ?? '';
    $agreeTerms = isset($_POST['agree_terms']);

    // 验证输入
    if (empty($username)) {
        throw new Exception('请输入用户名');
    }
    if (strlen($username) < 3 || strlen($username) > 20) {
        throw new Exception('用户名长度应在3-20个字符之间');
    }
    if (!preg_match('/^[a-zA-Z0-9_\x{4e00}-\x{9fa5}]+$/u', $username)) {
        throw new Exception('用户名只能包含字母、数字、下划线和中文');
    }

    if (empty($email)) {
        throw new Exception('请输入邮箱地址');
    }
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('邮箱格式不正确');
    }

    if (empty($phone)) {
        throw new Exception('请输入手机号');
    }
    if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
        throw new Exception('手机号格式不正确');
    }

    if (empty($password)) {
        throw new Exception('请输入密码');
    }
    if (strlen($password) < 6) {
        throw new Exception('密码长度至少6位');
    }

    if ($password !== $confirmPassword) {
        throw new Exception('两次输入的密码不一致');
    }

    if (empty($smsCode)) {
        throw new Exception('请输入短信验证码');
    }

    if (!$agreeTerms) {
        throw new Exception('请同意用户协议和隐私政策');
    }

    // 连接数据库
    $pdo = new PDO($dsn, $username_db, $password_db, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4"
    ]);

    // 验证短信验证码（开发环境使用固定验证码）
    if ($smsCode !== '123456' && !verifySmsCode($pdo, $phone, $smsCode, 'register')) {
        throw new Exception('验证码错误或已过期');
    }

    // 检查用户名是否已存在
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? AND deleted_at IS NULL");
    $stmt->execute([$username]);
    if ($stmt->fetch()) {
        throw new Exception('用户名已存在');
    }

    // 检查邮箱是否已存在
    $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ? AND deleted_at IS NULL");
    $stmt->execute([$email]);
    if ($stmt->fetch()) {
        throw new Exception('邮箱已被注册');
    }

    // 检查手机号是否已存在
    $stmt = $pdo->prepare("SELECT id FROM users WHERE phone = ? AND deleted_at IS NULL");
    $stmt->execute([$phone]);
    if ($stmt->fetch()) {
        throw new Exception('手机号已被注册');
    }

    // 开始事务
    $pdo->beginTransaction();

    try {
        // 创建用户
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("
            INSERT INTO users (username, email, phone, password, status, user_type, created_at, updated_at) 
            VALUES (?, ?, ?, ?, 'active', 'user', NOW(), NOW())
        ");
        $stmt->execute([$username, $email, $phone, $hashedPassword]);
        $userId = $pdo->lastInsertId();

        // 创建用户资料
        $stmt = $pdo->prepare("
            INSERT INTO user_profiles (user_id, nickname, created_at, updated_at) 
            VALUES (?, ?, NOW(), NOW())
        ");
        $stmt->execute([$userId, $username]);

        // 记录注册日志
        logUserAction($pdo, $userId, 'register', '用户注册', [
            'ip' => $_SERVER['REMOTE_ADDR'] ?? '',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);

        // 提交事务
        $pdo->commit();

        // 自动登录
        session_start();
        $_SESSION['user_id'] = $userId;
        $_SESSION['username'] = $username;
        $_SESSION['email'] = $email;
        $_SESSION['phone'] = $phone;
        $_SESSION['avatar'] = '';
        $_SESSION['user_type'] = 'user';
        $_SESSION['login_time'] = time();

        // 返回成功响应
        echo json_encode([
            'success' => true,
            'message' => '注册成功',
            'user' => [
                'id' => $userId,
                'username' => $username,
                'email' => $email,
                'phone' => $phone,
                'avatar' => '',
                'user_type' => 'user'
            ],
            'redirect' => 'index.php'
        ]);

    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * 验证短信验证码
 */
function verifySmsCode($pdo, $phone, $code, $type = 'register') {
    $stmt = $pdo->prepare("
        SELECT * FROM sms_codes 
        WHERE phone = ? AND code = ? AND type = ? AND used = 0 
        AND expires_at > NOW() 
        ORDER BY created_at DESC 
        LIMIT 1
    ");
    $stmt->execute([$phone, $code, $type]);
    $smsRecord = $stmt->fetch();

    if (!$smsRecord) {
        return false;
    }

    // 标记验证码为已使用
    $stmt = $pdo->prepare("UPDATE sms_codes SET used = 1 WHERE id = ?");
    $stmt->execute([$smsRecord['id']]);

    return true;
}

/**
 * 记录用户操作日志
 */
function logUserAction($pdo, $userId, $action, $description, $data = []) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO user_logs (user_id, action, description, data, ip_address, user_agent, created_at) 
            VALUES (?, ?, ?, ?, ?, ?, NOW())
        ");
        $stmt->execute([
            $userId,
            $action,
            $description,
            json_encode($data),
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (Exception $e) {
        // 日志记录失败不影响主流程
        error_log('Failed to log user action: ' . $e->getMessage());
    }
}
?>
